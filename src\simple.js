// 排行榜API处理
async function handleLeaderboardAPI(request, env, url) {
  const difficulty = url.pathname.split('/').pop();
  
  if (request.method === 'GET') {
    try {
      const data = await env.LEADERBOARD.get('leaderboard:' + difficulty);
      const leaderboard = data ? JSON.parse(data) : [];
      return new Response(JSON.stringify({ success: true, data: leaderboard }), {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      return new Response(JSON.stringify({ success: false, error: error.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }
  
  if (request.method === 'POST') {
    try {
      const { username, time } = await request.json();
      
      if (!username || !time) {
        return new Response(JSON.stringify({ success: false, error: '缺少必要参数' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      
      const data = await env.LEADERBOARD.get('leaderboard:' + difficulty);
      const leaderboard = data ? JSON.parse(data) : [];
      
      leaderboard.push({ username, time, date: new Date().toISOString() });
      leaderboard.sort((a, b) => a.time - b.time);
      const top10 = leaderboard.slice(0, 10);
      
      await env.LEADERBOARD.put('leaderboard:' + difficulty, JSON.stringify(top10));
      
      return new Response(JSON.stringify({ success: true, data: top10 }), {
        headers: { 'Content-Type': 'application/json' },
      });
    } catch (error) {
      return new Response(JSON.stringify({ success: false, error: error.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }
  
  return new Response('Method not allowed', { status: 405 });
}

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    if (url.pathname.startsWith('/api/leaderboard/')) {
      return handleLeaderboardAPI(request, env, url);
    }

    if (url.pathname === '/') {
      return new Response(getGameHTML(), {
        headers: {
          'Content-Type': 'text/html;charset=UTF-8',
          'Cache-Control': 'public, max-age=3600',
        },
      });
    }

    return new Response('Not Found', { status: 404 });
  },
};

function getGameHTML() {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经典扫雷 - Classic Minesweeper</title>
    <style>
        :root {
            --cell-size: 30px;
            --counter-font-size: 24px;
            --smiley-size: 40px;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'MS Sans Serif', sans-serif;
            background-color: #c0c0c0;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            user-select: none;
            padding: 20px 10px;
        }
        .main-container {
            display: flex;
            gap: 20px;
            width: 100%;
            max-width: 1600px;
            align-items: flex-start;
            justify-content: center;
        }
        .game-container {
            flex: 1;
            min-width: 400px;
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .difficulty-selector {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .difficulty-buttons {
            display: flex;
            gap: 5px;
        }
        .difficulty-button {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            min-width: 60px;
        }
        .difficulty-button:active {
            border: 2px inset #c0c0c0;
        }
        .difficulty-button.active {
            border: 2px inset #c0c0c0;
            background-color: #a0a0a0;
        }
        .help-button {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
        }
        .help-button:active {
            border: 2px inset #c0c0c0;
        }
        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #c0c0c0;
            border: 2px inset #c0c0c0;
            padding: 3px 6px;
            margin-bottom: 10px;
            width: 100%;
        }
        .counter {
            background-color: #000;
            color: #ff0000;
            font-family: 'Courier New', monospace;
            font-size: var(--counter-font-size);
            font-weight: bold;
            padding: 3px 6px;
            border: 1px inset #808080;
            min-width: calc(var(--counter-font-size) * 2.5);
            text-align: center;
        }
        .smiley-button {
            width: var(--smiley-size);
            height: var(--smiley-size);
            font-size: calc(var(--smiley-size) * 0.6);
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .smiley-button:active {
            border: 2px inset #c0c0c0;
        }
        .game-board {
            border: 2px inset #c0c0c0;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #c0c0c0;
            padding: 2px;
        }
        .board-grid {
            display: grid;
            gap: 0;
            border: 1px solid #808080;
        }
        .cell {
            width: var(--cell-size);
            height: var(--cell-size);
            border: 1px outset #c0c0c0;
            background-color: #c0c0c0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: calc(var(--cell-size) * 0.6);
            font-weight: bold;
            cursor: pointer;
        }
        .cell:active {
            border: 1px inset #c0c0c0;
        }
        .cell.revealed {
            border: 1px solid #808080;
            background-color: #ffffff;
        }
        .cell.mine {
            background-color: #ff0000;
        }
        .cell.flagged::after {
            content: '🚩';
            font-size: calc(var(--cell-size) * 0.7);
        }
        .cell.number-1 { color: #0000ff; }
        .cell.number-2 { color: #008000; }
        .cell.number-3 { color: #ff0000; }
        .cell.number-4 { color: #000080; }
        .cell.number-5 { color: #800000; }
        .cell.number-6 { color: #008080; }
        .cell.number-7 { color: #000000; }
        .cell.number-8 { color: #808080; }
        
        /* 排行榜面板样式 */
        .leaderboard-panel {
            width: 280px;
            background-color: #c0c0c0;
            border: 2px inset #c0c0c0;
            padding: 10px;
            height: fit-content;
            max-height: 80vh;
            overflow-y: auto;
        }
        .leaderboard-header h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            text-align: center;
            color: #000080;
        }
        .leaderboard-tabs {
            display: flex;
            gap: 2px;
            margin-bottom: 15px;
        }
        .tab-button {
            flex: 1;
            padding: 6px 4px;
            font-size: 11px;
            background: #c0c0c0;
            border: 1px outset #c0c0c0;
            cursor: pointer;
        }
        .tab-button.active {
            background: #ffffff;
            border: 1px inset #c0c0c0;
        }
        .leaderboard-list {
            display: block;
        }
        .leaderboard-item {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            margin: 2px 0;
            background: #f0f0f0;
            border: 1px solid #808080;
            font-size: 12px;
        }
        .leaderboard-rank {
            font-weight: bold;
            color: #666;
            min-width: 25px;
            text-align: center;
        }
        .leaderboard-username {
            flex: 1;
            margin: 0 8px;
            font-weight: bold;
        }
        .leaderboard-time {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #d00;
            font-size: 11px;
        }
        
        /* 美化的模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            background-color: #c0c0c0;
            margin: 15% auto;
            padding: 0;
            border: 3px outset #c0c0c0;
            width: 90%;
            max-width: 400px;
            border-radius: 0;
            animation: slideIn 0.3s ease-out;
        }

        .modal-header {
            background: linear-gradient(90deg, #000080, #0000ff);
            color: white;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-close {
            background: #c0c0c0;
            border: 1px outset #c0c0c0;
            width: 20px;
            height: 18px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:active {
            border: 1px inset #c0c0c0;
        }

        .modal-body {
            padding: 20px;
            text-align: center;
            font-size: 14px;
            line-height: 1.5;
        }

        .modal-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .modal-buttons {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .modal-button {
            background: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            min-width: 80px;
        }

        .modal-button:active {
            border: 2px inset #c0c0c0;
        }

        .modal-button.primary {
            background: #0078d4;
            color: white;
            border: 2px outset #0078d4;
        }

        .modal-button.primary:active {
            border: 2px inset #0078d4;
        }

        .modal-input {
            width: 100%;
            padding: 6px;
            border: 2px inset #c0c0c0;
            font-size: 14px;
            margin: 10px 0;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @media (max-width: 768px) {
            .leaderboard-panel {
                display: none;
            }
            .modal-content {
                width: 95%;
                margin: 20% auto;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 排行榜面板 -->
        <div class="leaderboard-panel">
            <div class="leaderboard-header">
                <h3>🏆 排行榜</h3>
                <div class="leaderboard-tabs">
                    <button class="tab-button active" onclick="switchLeaderboard('beginner')">初级</button>
                    <button class="tab-button" onclick="switchLeaderboard('intermediate')">中级</button>
                    <button class="tab-button" onclick="switchLeaderboard('expert')">专家</button>
                </div>
            </div>
            <div class="leaderboard-list" id="leaderboard-list">
                <div style="text-align: center; padding: 20px; color: #666;">加载中...</div>
            </div>
        </div>

        <!-- 游戏区域 -->
        <div class="game-container">
            <div class="difficulty-selector">
                <div class="difficulty-buttons">
                    <button class="difficulty-button active" onclick="setDifficulty('beginner')">初级</button>
                    <button class="difficulty-button" onclick="setDifficulty('intermediate')">中级</button>
                    <button class="difficulty-button" onclick="setDifficulty('expert')">专家</button>
                </div>
                <button class="help-button" onclick="showHelp()">帮助</button>
            </div>

            <div class="game-header">
                <div class="counter" id="mine-counter">010</div>
                <button class="smiley-button" id="smiley-button" onclick="newGame()">😊</button>
                <div class="counter" id="timer">000</div>
            </div>

            <div class="game-board">
                <div class="board-grid" id="board-grid"></div>
            </div>
        </div>
    </div>

    <!-- 美化的模态框 -->
    <div id="game-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span id="modal-title">游戏提示</span>
                <button class="modal-close" onclick="closeModal()">×</button>
            </div>
            <div class="modal-body">
                <span id="modal-icon" class="modal-icon">😊</span>
                <div id="modal-message">消息内容</div>
                <div id="modal-input-container" style="display: none;">
                    <input type="text" id="modal-input" class="modal-input" placeholder="请输入您的用户名" maxlength="20">
                </div>
                <div class="modal-buttons">
                    <button id="modal-cancel" class="modal-button" onclick="closeModal()" style="display: none;">取消</button>
                    <button id="modal-confirm" class="modal-button primary" onclick="handleModalConfirm()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化的扫雷游戏
        class MinesweeperGame {
            constructor() {
                this.difficulties = {
                    beginner: { rows: 9, cols: 9, mines: 10 },
                    intermediate: { rows: 16, cols: 16, mines: 40 },
                    expert: { rows: 16, cols: 30, mines: 99 }
                };
                this.currentDifficulty = 'beginner';
                this.board = [];
                this.revealed = [];
                this.flagged = [];
                this.gameState = 'ready';
                this.firstClick = true;
                this.startTime = null;
                this.timer = null;
                this.mineCount = 0;
                this.flagCount = 0;
            }

            initGame() {
                const config = this.difficulties[this.currentDifficulty];
                this.rows = config.rows;
                this.cols = config.cols;
                this.mineCount = config.mines;
                this.flagCount = 0;

                this.board = Array(this.rows).fill().map(() => Array(this.cols).fill(0));
                this.revealed = Array(this.rows).fill().map(() => Array(this.cols).fill(false));
                this.flagged = Array(this.rows).fill().map(() => Array(this.cols).fill(false));

                this.gameState = 'ready';
                this.firstClick = true;
                this.startTime = null;

                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }

                this.createBoard();
                this.updateDisplay();

                document.getElementById('smiley-button').textContent = '😊';
                document.getElementById('timer').textContent = '000';
            }

            createBoard() {
                const boardGrid = document.getElementById('board-grid');
                boardGrid.innerHTML = '';

                // 动态计算格子大小
                this.calculateOptimalCellSize();

                boardGrid.style.gridTemplateColumns = 'repeat(' + this.cols + ', var(--cell-size))';
                boardGrid.style.gridTemplateRows = 'repeat(' + this.rows + ', var(--cell-size))';

                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        const cell = document.createElement('div');
                        cell.className = 'cell';
                        cell.addEventListener('click', (e) => this.handleLeftClick(row, col, e));
                        cell.addEventListener('contextmenu', (e) => this.handleRightClick(row, col, e));
                        boardGrid.appendChild(cell);
                    }
                }
            }

            // 计算最佳格子大小，确保所有内容都在视口内
            calculateOptimalCellSize() {
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // 精确测量实际占用的空间
                const leaderboardPanel = document.querySelector('.leaderboard-panel');
                let leaderboardWidth = 0;
                if (leaderboardPanel && window.getComputedStyle(leaderboardPanel).display !== 'none') {
                    leaderboardWidth = leaderboardPanel.offsetWidth + 20; // 包括间距
                }

                // 精确测量顶部控件高度
                const difficultySelector = document.querySelector('.difficulty-selector');
                const gameHeader = document.querySelector('.game-header');
                let topHeight = 0;
                if (difficultySelector && gameHeader) {
                    topHeight = difficultySelector.offsetHeight + gameHeader.offsetHeight + 30; // 包括边距
                }

                // 计算游戏板的最大可用空间（留出安全边距）
                const safeMargin = 20;
                const maxBoardWidth = viewportWidth - leaderboardWidth - safeMargin * 2;
                const maxBoardHeight = viewportHeight - topHeight - safeMargin * 2;

                // 确保最小可用空间
                const minBoardWidth = Math.max(maxBoardWidth, 200);
                const minBoardHeight = Math.max(maxBoardHeight, 150);

                // 计算格子大小，确保游戏板完全适应可用空间
                const maxCellSizeByWidth = Math.floor(minBoardWidth / this.cols);
                const maxCellSizeByHeight = Math.floor(minBoardHeight / this.rows);

                // 取较小值确保完整显示
                let optimalSize = Math.min(maxCellSizeByWidth, maxCellSizeByHeight);

                // 设置合理的大小范围，但优先保证完整显示
                optimalSize = Math.max(12, Math.min(45, optimalSize));

                // 最终验证：确保计算出的游戏板尺寸不会超出视口
                const finalBoardWidth = optimalSize * this.cols;
                const finalBoardHeight = optimalSize * this.rows;
                const totalWidth = finalBoardWidth + leaderboardWidth + safeMargin * 2;
                const totalHeight = finalBoardHeight + topHeight + safeMargin * 2;

                // 如果仍然超出，进一步缩小
                if (totalWidth > viewportWidth || totalHeight > viewportHeight) {
                    const scaleX = viewportWidth / totalWidth;
                    const scaleY = viewportHeight / totalHeight;
                    const scale = Math.min(scaleX, scaleY) * 0.95; // 留5%安全边距
                    optimalSize = Math.floor(optimalSize * scale);
                    optimalSize = Math.max(10, optimalSize); // 绝对最小值
                }

                // 更新CSS变量
                document.documentElement.style.setProperty('--cell-size', optimalSize + 'px');
                document.documentElement.style.setProperty('--counter-font-size', Math.max(12, optimalSize * 0.7) + 'px');
                document.documentElement.style.setProperty('--smiley-size', Math.max(24, optimalSize * 1.0) + 'px');

                console.log('布局计算结果:', {
                    视口: viewportWidth + 'x' + viewportHeight,
                    排行榜宽度: leaderboardWidth,
                    顶部高度: topHeight,
                    可用空间: minBoardWidth + 'x' + minBoardHeight,
                    游戏尺寸: this.cols + 'x' + this.rows,
                    格子大小: optimalSize,
                    最终游戏板: finalBoardWidth + 'x' + finalBoardHeight
                });
            }

            generateMines(firstClickRow, firstClickCol) {
                const positions = [];
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (Math.abs(row - firstClickRow) <= 1 && Math.abs(col - firstClickCol) <= 1) {
                            continue;
                        }
                        positions.push([row, col]);
                    }
                }

                for (let i = positions.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [positions[i], positions[j]] = [positions[j], positions[i]];
                }

                for (let i = 0; i < this.mineCount && i < positions.length; i++) {
                    const [row, col] = positions[i];
                    this.board[row][col] = -1;
                }

                this.calculateNumbers();
            }

            calculateNumbers() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (this.board[row][col] !== -1) {
                            let count = 0;
                            for (let dr = -1; dr <= 1; dr++) {
                                for (let dc = -1; dc <= 1; dc++) {
                                    const newRow = row + dr;
                                    const newCol = col + dc;
                                    if (this.isValidCell(newRow, newCol) && this.board[newRow][newCol] === -1) {
                                        count++;
                                    }
                                }
                            }
                            this.board[row][col] = count;
                        }
                    }
                }
            }

            isValidCell(row, col) {
                return row >= 0 && row < this.rows && col >= 0 && col < this.cols;
            }

            handleLeftClick(row, col, event) {
                event.preventDefault();
                if (this.gameState === 'won' || this.gameState === 'lost') return;
                if (this.flagged[row][col]) return;

                if (this.firstClick) {
                    this.generateMines(row, col);
                    this.firstClick = false;
                    this.gameState = 'playing';
                    this.startTimer();
                }

                this.revealCell(row, col);
                this.updateDisplay();
                this.checkGameState();
            }

            handleRightClick(row, col, event) {
                event.preventDefault();
                if (this.gameState === 'won' || this.gameState === 'lost') return;
                if (this.revealed[row][col]) return;

                this.flagged[row][col] = !this.flagged[row][col];
                this.flagCount += this.flagged[row][col] ? 1 : -1;
                this.updateDisplay();
            }

            revealCell(row, col) {
                if (!this.isValidCell(row, col) || this.revealed[row][col] || this.flagged[row][col]) {
                    return;
                }

                this.revealed[row][col] = true;

                if (this.board[row][col] === -1) {
                    this.gameState = 'lost';
                    this.revealAllMines();
                    return;
                }

                if (this.board[row][col] === 0) {
                    for (let dr = -1; dr <= 1; dr++) {
                        for (let dc = -1; dc <= 1; dc++) {
                            this.revealCell(row + dr, col + dc);
                        }
                    }
                }
            }

            revealAllMines() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (this.board[row][col] === -1) {
                            this.revealed[row][col] = true;
                        }
                    }
                }
            }

            checkGameState() {
                if (this.gameState === 'lost') {
                    document.getElementById('smiley-button').textContent = '😵';
                    this.stopTimer();
                    setTimeout(() => {
                        showModal('游戏失败', '💣', '踩到地雷了！<br>点击笑脸或新游戏按钮重新开始。');
                    }, 100);
                    return;
                }

                let unrevealedCount = 0;
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (!this.revealed[row][col] && this.board[row][col] !== -1) {
                            unrevealedCount++;
                        }
                    }
                }

                if (unrevealedCount === 0) {
                    this.gameState = 'won';
                    document.getElementById('smiley-button').textContent = '😎';
                    this.stopTimer();

                    for (let row = 0; row < this.rows; row++) {
                        for (let col = 0; col < this.cols; col++) {
                            if (this.board[row][col] === -1 && !this.flagged[row][col]) {
                                this.flagged[row][col] = true;
                                this.flagCount++;
                            }
                        }
                    }

                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                    setTimeout(async () => {
                        const message = '用时：' + elapsed + '秒<br>难度：' + this.getDifficultyName() + '<br><br>太棒了！你成功找出了所有地雷！<br>请输入您的用户名上传成绩：';
                        const username = await showModal('恭喜胜利！', '🎉', message, true, true);
                        if (username && username.trim()) {
                            uploadScore(username.trim(), elapsed, this.currentDifficulty);
                        }
                    }, 100);
                }
            }

            startTimer() {
                this.startTime = Date.now();
                this.timer = setInterval(() => {
                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                    const displayTime = Math.min(elapsed, 999);
                    document.getElementById('timer').textContent = displayTime.toString().padStart(3, '0');
                }, 1000);
            }

            stopTimer() {
                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
            }

            getDifficultyName() {
                const names = {
                    beginner: '初级',
                    intermediate: '中级',
                    expert: '专家'
                };
                return names[this.currentDifficulty] || '未知';
            }

            updateDisplay() {
                const remainingMines = this.mineCount - this.flagCount;
                document.getElementById('mine-counter').textContent =
                    Math.max(-99, Math.min(999, remainingMines)).toString().padStart(3, '0');

                const cells = document.querySelectorAll('.cell');
                cells.forEach((cell, index) => {
                    const row = Math.floor(index / this.cols);
                    const col = index % this.cols;

                    cell.className = 'cell';
                    cell.textContent = '';

                    if (this.flagged[row][col]) {
                        cell.classList.add('flagged');
                    } else if (this.revealed[row][col]) {
                        cell.classList.add('revealed');
                        if (this.board[row][col] === -1) {
                            cell.classList.add('mine');
                            cell.textContent = '💣';
                        } else if (this.board[row][col] > 0) {
                            cell.classList.add('number-' + this.board[row][col]);
                            cell.textContent = this.board[row][col];
                        }
                    }
                });
            }
        }

        // 全局变量
        let game = null;
        let currentLeaderboardDifficulty = 'beginner';
        let modalCallback = null;

        // 模态框函数
        function showModal(title, icon, message, showInput = false, showCancel = false) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-icon').textContent = icon;
            document.getElementById('modal-message').innerHTML = message;

            const inputContainer = document.getElementById('modal-input-container');
            const cancelButton = document.getElementById('modal-cancel');
            const input = document.getElementById('modal-input');

            if (showInput) {
                inputContainer.style.display = 'block';
                input.value = '';
                setTimeout(() => input.focus(), 100);
            } else {
                inputContainer.style.display = 'none';
            }

            cancelButton.style.display = showCancel ? 'inline-block' : 'none';

            document.getElementById('game-modal').style.display = 'block';

            return new Promise((resolve) => {
                modalCallback = resolve;
            });
        }

        function closeModal() {
            document.getElementById('game-modal').style.display = 'none';
            if (modalCallback) {
                modalCallback(null);
                modalCallback = null;
            }
        }

        function handleModalConfirm() {
            const input = document.getElementById('modal-input');
            const value = input.style.display !== 'none' && input.offsetParent !== null ? input.value.trim() : true;

            document.getElementById('game-modal').style.display = 'none';
            if (modalCallback) {
                modalCallback(value);
                modalCallback = null;
            }
        }

        // 点击模态框背景关闭
        document.addEventListener('click', (e) => {
            if (e.target.id === 'game-modal') {
                closeModal();
            }
        });

        // 全局函数
        function setDifficulty(difficulty) {
            if (!game) return;

            document.querySelectorAll('.difficulty-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            game.currentDifficulty = difficulty;
            game.initGame();
        }

        function newGame() {
            if (game) {
                game.initGame();
            }
        }

        function showHelp() {
            const helpMessage =
                '<strong>桌面端：</strong><br>' +
                '左键点击挖掘格子<br>' +
                '右键点击标记地雷<br><br>' +

                '<strong>移动端：</strong><br>' +
                '点击挖掘格子<br>' +
                '长按标记地雷<br><br>' +

                '<strong>难度选择：</strong><br>' +
                '• 初级：9×9，10个地雷<br>' +
                '• 中级：16×16，40个地雷<br>' +
                '• 专家：30×16，99个地雷<br><br>' +

                '<strong>提示：</strong><br>' +
                '数字表示周围8个格子中地雷的数量';
            showModal('游戏帮助', '❓', helpMessage);
        }

        function switchLeaderboard(difficulty) {
            currentLeaderboardDifficulty = difficulty;

            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            loadLeaderboard(difficulty);
        }

        async function loadLeaderboard(difficulty) {
            try {
                const response = await fetch('/api/leaderboard/' + difficulty);
                const result = await response.json();

                const listElement = document.getElementById('leaderboard-list');
                if (result.success && result.data.length > 0) {
                    listElement.innerHTML = result.data.map((record, index) => {
                        const rank = index + 1;
                        return '<div class="leaderboard-item">' +
                               '<div class="leaderboard-rank">' + rank + '</div>' +
                               '<div class="leaderboard-username">' + record.username + '</div>' +
                               '<div class="leaderboard-time">' + record.time + 's</div>' +
                               '</div>';
                    }).join('');
                } else {
                    listElement.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">暂无记录</div>';
                }
            } catch (error) {
                console.error('加载排行榜失败:', error);
                document.getElementById('leaderboard-list').innerHTML =
                    '<div style="text-align: center; padding: 20px; color: #d00;">加载失败</div>';
            }
        }

        async function uploadScore(username, time, difficulty) {
            try {
                const response = await fetch('/api/leaderboard/' + difficulty, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, time })
                });

                const result = await response.json();
                if (result.success) {
                    showModal('上传成功', '🎉', '成绩上传成功！<br>您的成绩已添加到排行榜中。');
                    if (currentLeaderboardDifficulty === difficulty) {
                        loadLeaderboard(difficulty);
                    }
                } else {
                    showModal('上传失败', '❌', '上传失败：' + result.error);
                }
            } catch (error) {
                console.error('上传成绩失败:', error);
                showModal('上传失败', '❌', '上传失败：网络连接错误<br>请检查网络连接后重试。');
            }
        }

        // 初始化
        window.addEventListener('DOMContentLoaded', () => {
            game = new MinesweeperGame();
            game.initGame(); // 确保游戏板被创建
            loadLeaderboard('beginner');

            // 添加窗口大小变化监听器
            window.addEventListener('resize', () => {
                if (game) {
                    game.calculateOptimalCellSize();
                }
            });
        });
    </script>
</body>
</html>`;
}
