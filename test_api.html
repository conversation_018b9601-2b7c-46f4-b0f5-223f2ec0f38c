<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排行榜API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            cursor: pointer;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        .success { background: #d4edda; }
        .error { background: #f8d7da; }
    </style>
</head>
<body>
    <h1>🏆 排行榜API测试工具</h1>
    
    <div class="test-section">
        <h3>📊 获取排行榜</h3>
        <button onclick="testGetLeaderboard('beginner')">获取初级排行榜</button>
        <button onclick="testGetLeaderboard('intermediate')">获取中级排行榜</button>
        <button onclick="testGetLeaderboard('expert')">获取专家排行榜</button>
        <div id="get-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>📤 上传成绩</h3>
        <input type="text" id="username" placeholder="用户名" value="测试玩家">
        <input type="number" id="time" placeholder="时间(秒)" value="120">
        <select id="difficulty">
            <option value="beginner">初级</option>
            <option value="intermediate">中级</option>
            <option value="expert">专家</option>
        </select>
        <button onclick="testUploadScore()">上传成绩</button>
        <div id="upload-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>🧪 批量测试数据</h3>
        <button onclick="createTestData()">创建测试数据</button>
        <div id="batch-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8787/api/leaderboard';
        
        async function testGetLeaderboard(difficulty) {
            const resultDiv = document.getElementById('get-result');
            resultDiv.textContent = '正在获取排行榜...';
            
            try {
                const response = await fetch(`${API_BASE}/${difficulty}`);
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 获取成功！\n${JSON.stringify(result.data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 获取失败：${result.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误：${error.message}`;
            }
        }
        
        async function testUploadScore() {
            const resultDiv = document.getElementById('upload-result');
            const username = document.getElementById('username').value;
            const time = parseInt(document.getElementById('time').value);
            const difficulty = document.getElementById('difficulty').value;
            
            if (!username || !time) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请填写用户名和时间';
                return;
            }
            
            resultDiv.textContent = '正在上传成绩...';
            
            try {
                const response = await fetch(`${API_BASE}/${difficulty}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, time })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 上传成功！\n更新后的排行榜：\n${JSON.stringify(result.data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 上传失败：${result.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 网络错误：${error.message}`;
            }
        }
        
        async function createTestData() {
            const resultDiv = document.getElementById('batch-result');
            resultDiv.textContent = '正在创建测试数据...';
            
            const testData = [
                { difficulty: 'beginner', username: '闪电侠', time: 45 },
                { difficulty: 'beginner', username: '扫雷大师', time: 52 },
                { difficulty: 'beginner', username: '速度之王', time: 38 },
                { difficulty: 'intermediate', username: '中级高手', time: 180 },
                { difficulty: 'intermediate', username: '稳扎稳打', time: 220 },
                { difficulty: 'expert', username: '专家玩家', time: 480 },
                { difficulty: 'expert', username: '终极挑战者', time: 520 }
            ];
            
            let results = [];
            
            for (const data of testData) {
                try {
                    const response = await fetch(`${API_BASE}/${data.difficulty}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ username: data.username, time: data.time })
                    });
                    
                    const result = await response.json();
                    results.push(`${data.difficulty} - ${data.username}: ${result.success ? '✅' : '❌'}`);
                } catch (error) {
                    results.push(`${data.difficulty} - ${data.username}: ❌ ${error.message}`);
                }
            }
            
            resultDiv.className = 'result success';
            resultDiv.textContent = `测试数据创建完成：\n${results.join('\n')}`;
        }
    </script>
</body>
</html>
