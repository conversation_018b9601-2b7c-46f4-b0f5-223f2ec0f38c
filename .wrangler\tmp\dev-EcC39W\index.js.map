{"version": 3, "sources": ["../bundle-hQIGZL/checked-fetch.js", "../bundle-hQIGZL/strip-cf-connecting-ip-header.js", "../../../src/index.js", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-hQIGZL/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-hQIGZL/middleware-loader.entry.ts"], "sourceRoot": "E:\\Work\\mine\\.wrangler\\tmp\\dev-EcC39W", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "function stripCfConnectingIPHeader(input, init) {\n\tconst request = new Request(input, init);\n\trequest.headers.delete(\"CF-Connecting-IP\");\n\treturn request;\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\treturn Reflect.apply(target, thisArg, [\n\t\t\tstripCfConnectingIPHeader.apply(null, argArray),\n\t\t]);\n\t},\n});\n", "// 经典扫雷游戏 - <PERSON>flare Workers 版本\n// 作者: Claude 4.0 sonnet\n\n// 排行榜API处理函数\nasync function handleLeaderboardAPI(request, env, url) {\n  const pathParts = url.pathname.split('/');\n  const difficulty = pathParts[3]; // /api/leaderboard/{difficulty}\n\n  // 验证难度参数\n  if (!['beginner', 'intermediate', 'expert'].includes(difficulty)) {\n    return new Response(JSON.stringify({ success: false, error: '无效的难度级别' }), {\n      status: 400,\n      headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }\n    });\n  }\n\n  try {\n    if (request.method === 'GET') {\n      // 获取排行榜\n      const leaderboard = await getLeaderboard(env, difficulty);\n      return new Response(JSON.stringify({ success: true, data: leaderboard }), {\n        headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }\n      });\n    } else if (request.method === 'POST') {\n      // 上传成绩\n      const body = await request.json();\n      const { username, time } = body;\n\n      // 验证数据\n      const validation = validateScore(username, time, difficulty);\n      if (!validation.valid) {\n        return new Response(JSON.stringify({ success: false, error: validation.error }), {\n          status: 400,\n          headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }\n        });\n      }\n\n      // 更新排行榜\n      const updatedLeaderboard = await updateLeaderboard(env, difficulty, username, time);\n      return new Response(JSON.stringify({ success: true, data: updatedLeaderboard }), {\n        headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }\n      });\n    } else if (request.method === 'OPTIONS') {\n      // 处理CORS预检请求\n      return new Response(null, {\n        headers: {\n          'Access-Control-Allow-Origin': '*',\n          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n          'Access-Control-Allow-Headers': 'Content-Type',\n        }\n      });\n    }\n  } catch (error) {\n    console.error('排行榜API错误:', error);\n    return new Response(JSON.stringify({ success: false, error: '服务器内部错误' }), {\n      status: 500,\n      headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }\n    });\n  }\n\n  return new Response('Method Not Allowed', { status: 405 });\n}\n\n// 获取排行榜数据\nasync function getLeaderboard(env, difficulty) {\n  try {\n    const data = await env.LEADERBOARD.get('leaderboard:' + difficulty);\n    return data ? JSON.parse(data) : [];\n  } catch (error) {\n    console.error('获取排行榜失败:', error);\n    return [];\n  }\n}\n\n// 更新排行榜数据\nasync function updateLeaderboard(env, difficulty, username, time) {\n  try {\n    const leaderboard = await getLeaderboard(env, difficulty);\n\n    // 移除同用户名的旧记录\n    const filtered = leaderboard.filter(record => record.username !== username);\n\n    // 添加新记录\n    filtered.push({\n      username: username.trim().substring(0, 20), // 限制长度\n      time: parseInt(time),\n      timestamp: Date.now()\n    });\n\n    // 按时间排序，保留前10名\n    const sorted = filtered.sort((a, b) => a.time - b.time).slice(0, 10);\n\n    await env.LEADERBOARD.put('leaderboard:' + difficulty, JSON.stringify(sorted));\n    return sorted;\n  } catch (error) {\n    console.error('更新排行榜失败:', error);\n    throw error;\n  }\n}\n\n// 验证成绩数据\nfunction validateScore(username, time, difficulty) {\n  // 用户名验证：1-20字符，只允许字母、数字、中文、下划线、连字符\n  if (!username || username.length === 0 || username.length > 20) {\n    return { valid: false, error: '用户名长度必须在1-20字符之间' };\n  }\n\n  if (!/^[a-zA-Z0-9\\u4e00-\\u9fa5_-]+$/.test(username)) {\n    return { valid: false, error: '用户名只能包含字母、数字、中文、下划线和连字符' };\n  }\n\n  // 时间验证：合理范围内的正整数\n  const minTimes = { beginner: 1, intermediate: 10, expert: 30 };\n  const maxTimes = { beginner: 999, intermediate: 9999, expert: 9999 };\n\n  if (!Number.isInteger(time) || time < minTimes[difficulty] || time > maxTimes[difficulty]) {\n    return { valid: false, error: '成绩数据异常，请重新游戏' };\n  }\n\n  return { valid: true };\n}\n\nexport default {\n  async fetch(request, env, ctx) {\n    const url = new URL(request.url);\n\n    // 处理排行榜API路由\n    if (url.pathname.startsWith('/api/leaderboard/')) {\n      return handleLeaderboardAPI(request, env, url);\n    }\n\n    if (url.pathname === '/') {\n      return new Response(getGameHTML(), {\n        headers: {\n          'Content-Type': 'text/html;charset=UTF-8',\n          'Cache-Control': 'public, max-age=3600',\n        },\n      });\n    }\n\n    return new Response('Not Found', { status: 404 });\n  },\n};\n\nfunction getGameHTML() {\n  return `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>经典扫雷 - Classic Minesweeper</title>\n    <style>\n        /* 经典扫雷样式 - 完美复刻Windows风格 */\n        :root {\n            --cell-size: 30px;\n            --counter-font-size: 24px;\n            --smiley-size: 40px;\n        }\n\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n\n        body {\n            font-family: 'MS Sans Serif', sans-serif;\n            background-color: #c0c0c0;\n            display: flex;\n            justify-content: center;\n            align-items: flex-start;\n            min-height: 100vh;\n            user-select: none;\n            margin: 0;\n            padding: 20px 10px;\n            box-sizing: border-box;\n        }\n\n        .main-container {\n            display: flex;\n            gap: 20px;\n            width: 100%;\n            max-width: 1600px;\n            align-items: flex-start;\n            justify-content: center;\n        }\n\n        .game-container {\n            flex: 1;\n            min-width: 400px;\n            background-color: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            padding: 10px;\n            border-radius: 0;\n            box-shadow: none;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n        }\n\n\n\n        .difficulty-selector {\n            margin-bottom: 1px;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            width: 100%;\n        }\n\n        .difficulty-buttons {\n            display: flex;\n            gap: 5px;\n        }\n\n        .difficulty-button {\n            background-color: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            padding: 6px 12px;\n            cursor: pointer;\n            font-size: 12px;\n            font-weight: bold;\n            min-width: 60px;\n        }\n\n        .difficulty-button:active {\n            border: 2px inset #c0c0c0;\n        }\n\n        .difficulty-button.active {\n            border: 2px inset #c0c0c0;\n            background-color: #a0a0a0;\n        }\n\n        .help-button {\n            background-color: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            padding: 6px 12px;\n            cursor: pointer;\n            font-size: 12px;\n            font-weight: bold;\n        }\n\n        .help-button:active {\n            border: 2px inset #c0c0c0;\n        }\n\n        .help-button:hover {\n            background-color: #d0d0d0;\n        }\n\n        .game-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            background-color: #c0c0c0;\n            border: 2px inset #c0c0c0;\n            padding: 3px 6px;\n            margin-bottom: 1px;\n            width: 100%;\n        }\n\n        .counter {\n            background-color: #000;\n            color: #ff0000;\n            font-family: 'Courier New', monospace;\n            font-size: var(--counter-font-size);\n            font-weight: bold;\n            padding: 3px 6px;\n            border: 1px inset #808080;\n            min-width: calc(var(--counter-font-size) * 2.5);\n            text-align: center;\n        }\n\n        .smiley-button {\n            width: var(--smiley-size);\n            height: var(--smiley-size);\n            font-size: calc(var(--smiley-size) * 0.6);\n            background-color: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }\n\n        .smiley-button:active {\n            border: 2px inset #c0c0c0;\n        }\n\n        .game-board {\n            border: 2px inset #c0c0c0;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            background-color: #c0c0c0;\n            padding: 2px;\n            margin-top: 1px;\n        }\n\n        .board-grid {\n            display: grid;\n            gap: 0;\n            border: 1px solid #808080;\n            min-width: 200px;\n            min-height: 200px;\n        }\n\n        .cell {\n            width: var(--cell-size);\n            height: var(--cell-size);\n            border: 1px outset #c0c0c0;\n            background-color: #c0c0c0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-size: calc(var(--cell-size) * 0.6);\n            font-weight: bold;\n            cursor: pointer;\n            position: relative;\n            min-width: 20px;\n            min-height: 20px;\n        }\n\n        .cell:active {\n            border: 1px inset #c0c0c0;\n        }\n\n        .cell.revealed {\n            border: 1px solid #808080;\n            background-color: #ffffff;\n        }\n\n        .cell.mine {\n            background-color: #ff0000;\n        }\n\n        .cell.flagged::after {\n            content: '🚩';\n            font-size: calc(var(--cell-size) * 0.7);\n        }\n\n        .cell.number-1 { color: #0000ff; }\n        .cell.number-2 { color: #008000; }\n        .cell.number-3 { color: #ff0000; }\n        .cell.number-4 { color: #000080; }\n        .cell.number-5 { color: #800000; }\n        .cell.number-6 { color: #008080; }\n        .cell.number-7 { color: #000000; }\n        .cell.number-8 { color: #808080; }\n\n        .cell.quick-dig-highlight {\n            background-color: #ffff99 !important;\n            border: 2px inset #ffff00 !important;\n        }\n\n        /* 排行榜面板样式 */\n        .leaderboard-panel {\n            width: 280px;\n            min-width: 280px;\n            flex-shrink: 0;\n            background-color: #c0c0c0;\n            border: 2px inset #c0c0c0;\n            padding: 10px;\n            height: fit-content;\n            max-height: 80vh;\n            overflow-y: auto;\n            display: block; /* 恢复显示 */\n        }\n\n        .leaderboard-header {\n            margin-bottom: 15px;\n        }\n\n        .leaderboard-header h3 {\n            margin: 0 0 10px 0;\n            font-size: 16px;\n            text-align: center;\n            color: #000080;\n        }\n\n        .leaderboard-tabs {\n            display: flex;\n            gap: 2px;\n        }\n\n        .tab-button {\n            flex: 1;\n            padding: 6px 4px;\n            font-size: 11px;\n            background: #c0c0c0;\n            border: 1px outset #c0c0c0;\n            cursor: pointer;\n            font-family: 'MS Sans Serif', sans-serif;\n        }\n\n        .tab-button:hover {\n            background: #d0d0d0;\n        }\n\n        .tab-button.active {\n            background: #ffffff;\n            border: 1px inset #c0c0c0;\n        }\n\n        .leaderboard-content {\n            position: relative;\n            min-height: 200px;\n        }\n\n        .leaderboard-loading {\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n            justify-content: center;\n            padding: 30px 10px;\n            color: #666;\n        }\n\n        .loading-spinner {\n            width: 20px;\n            height: 20px;\n            border: 2px solid #ddd;\n            border-top: 2px solid #666;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n            margin-bottom: 10px;\n        }\n\n        @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n        }\n\n        .leaderboard-list {\n            display: none;\n        }\n\n        .leaderboard-list.show {\n            display: block;\n        }\n\n        .leaderboard-item {\n            display: flex;\n            align-items: center;\n            padding: 8px 10px;\n            margin: 2px 0;\n            background: #f0f0f0;\n            border: 1px solid #808080;\n            font-size: 12px;\n        }\n\n        .leaderboard-item.highlight {\n            background: #ffff99;\n            border-color: #ff8000;\n        }\n\n        .leaderboard-rank {\n            font-weight: bold;\n            color: #666;\n            min-width: 25px;\n            text-align: center;\n        }\n\n        .leaderboard-rank.gold { color: #ffd700; }\n        .leaderboard-rank.silver { color: #c0c0c0; }\n        .leaderboard-rank.bronze { color: #cd7f32; }\n\n        .leaderboard-username {\n            flex: 1;\n            margin: 0 8px;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n            font-weight: bold;\n        }\n\n        .leaderboard-time {\n            font-family: 'Courier New', monospace;\n            font-weight: bold;\n            color: #d00;\n            font-size: 11px;\n        }\n\n        .leaderboard-empty {\n            text-align: center;\n            padding: 40px 20px;\n            color: #666;\n        }\n\n        .empty-icon {\n            font-size: 32px;\n            margin-bottom: 10px;\n        }\n\n        .empty-text {\n            font-size: 12px;\n            line-height: 1.4;\n        }\n\n        .leaderboard-toggle {\n            display: none;\n            padding: 6px 10px;\n            font-size: 14px;\n            background: #c0c0c0;\n            border: 1px outset #c0c0c0;\n            cursor: pointer;\n            margin-left: 10px;\n        }\n\n        .leaderboard-toggle:hover {\n            background: #d0d0d0;\n        }\n\n        .header-controls {\n            display: flex;\n            align-items: center;\n        }\n\n        /* 游戏结束提示框样式 */\n        .game-modal {\n            position: fixed;\n            top: 0;\n            left: 0;\n            width: 100%;\n            height: 100%;\n            background-color: rgba(0, 0, 0, 0.5);\n            display: none;\n            justify-content: center;\n            align-items: center;\n            z-index: 1000;\n            opacity: 0;\n            transition: opacity 0.3s ease;\n        }\n\n        .game-modal.show {\n            opacity: 1;\n        }\n\n        .modal-content {\n            background-color: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            padding: 25px;\n            text-align: center;\n            box-shadow: 4px 4px 12px rgba(0, 0, 0, 0.4);\n            max-width: 420px;\n            width: 90%;\n            transform: scale(0.8);\n            transition: transform 0.3s ease;\n        }\n\n        .game-modal.show .modal-content {\n            transform: scale(1);\n        }\n\n        .modal-title {\n            font-size: 24px;\n            font-weight: bold;\n            margin-bottom: 15px;\n            color: #000080;\n        }\n\n        .modal-icon {\n            font-size: 48px;\n            margin-bottom: 15px;\n        }\n\n        .modal-message {\n            font-size: 16px;\n            margin-bottom: 20px;\n            line-height: 1.4;\n        }\n\n        .modal-button {\n            background-color: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            padding: 8px 20px;\n            font-size: 14px;\n            cursor: pointer;\n            margin: 0 5px;\n        }\n\n        .modal-button:active {\n            border: 2px inset #c0c0c0;\n        }\n\n        .modal-button:hover {\n            background-color: #d0d0d0;\n        }\n\n        .modal-button.primary {\n            background-color: #0078d4;\n            color: white;\n            border-color: #0078d4;\n        }\n\n        .modal-button.primary:hover {\n            background-color: #106ebe;\n        }\n\n        .modal-buttons {\n            display: flex;\n            justify-content: center;\n            gap: 10px;\n            flex-wrap: wrap;\n        }\n\n        /* 上传对话框样式 */\n        .input-group {\n            margin: 20px 0;\n            text-align: left;\n        }\n\n        .input-group label {\n            display: block;\n            margin-bottom: 8px;\n            font-weight: bold;\n            color: #333;\n        }\n\n        .input-group input {\n            width: 100%;\n            padding: 8px 12px;\n            border: 2px inset #c0c0c0;\n            background: white;\n            font-size: 14px;\n            font-family: 'MS Sans Serif', sans-serif;\n            box-sizing: border-box;\n        }\n\n        .input-group input:focus {\n            outline: none;\n            border-color: #0078d4;\n        }\n\n        .input-hint {\n            font-size: 11px;\n            color: #666;\n            margin-top: 4px;\n        }\n\n        .upload-info {\n            background: #f0f0f0;\n            border: 1px solid #ccc;\n            padding: 10px;\n            margin-bottom: 15px;\n            font-weight: bold;\n        }\n\n        .button-spinner {\n            width: 14px;\n            height: 14px;\n            border: 2px solid transparent;\n            border-top: 2px solid white;\n            border-radius: 50%;\n            animation: spin 1s linear infinite;\n            margin-left: 8px;\n        }\n\n        /* 双键快速挖掘视觉反馈样式 */\n        .cell.quick-dig-highlight {\n            background: #ffff99 !important;\n            border: 2px solid #ff8000 !important;\n            box-shadow: 0 0 8px rgba(255, 128, 0, 0.6);\n            animation: quickDigPulse 0.5s ease-in-out infinite alternate;\n        }\n\n        .cell.quick-dig-active {\n            background: #90ee90 !important;\n            border: 2px solid #32cd32 !important;\n            transform: scale(1.05);\n            transition: all 0.15s ease;\n        }\n\n        @keyframes quickDigPulse {\n            0% {\n                box-shadow: 0 0 8px rgba(255, 128, 0, 0.6);\n                transform: scale(1);\n            }\n            100% {\n                box-shadow: 0 0 12px rgba(255, 128, 0, 0.9);\n                transform: scale(1.02);\n            }\n        }\n\n        /* 触摸设备优化 */\n        @media (hover: none) and (pointer: coarse) {\n            .cell {\n                min-height: 32px;\n                min-width: 32px;\n            }\n\n            .cell.quick-dig-highlight {\n                animation-duration: 0.3s;\n            }\n        }\n\n\n\n        /* 超大屏幕 - 显示排行榜 */\n        @media (min-width: 1200px) {\n            :root {\n                --cell-size: 35px;\n                --counter-font-size: 28px;\n                --smiley-size: 45px;\n            }\n\n            .leaderboard-panel {\n                display: block !important;\n            }\n\n            .leaderboard-toggle {\n                display: none;\n            }\n        }\n\n        /* 大屏幕 - 桌面端 */\n        @media (min-width: 1000px) and (max-width: 1199px) {\n            :root {\n                --cell-size: 32px;\n                --counter-font-size: 26px;\n                --smiley-size: 42px;\n            }\n\n            .leaderboard-panel {\n                display: block !important;\n            }\n\n            .leaderboard-toggle {\n                display: inline-block;\n            }\n        }\n\n        /* 中等屏幕 - 平板端 */\n        @media (min-width: 768px) and (max-width: 999px) {\n            :root {\n                --cell-size: 30px;\n                --counter-font-size: 24px;\n                --smiley-size: 40px;\n            }\n\n            .leaderboard-panel {\n                display: none;\n            }\n\n            .leaderboard-toggle {\n                display: inline-block;\n            }\n        }\n\n        /* 小屏幕 - 手机端 */\n        @media (max-width: 767px) {\n            :root {\n                --cell-size: 28px;\n                --counter-font-size: 22px;\n                --smiley-size: 38px;\n            }\n\n            body {\n                padding: 10px 5px;\n            }\n\n            .leaderboard-panel {\n                display: none;\n            }\n\n            .leaderboard-toggle {\n                display: inline-block;\n            }\n        }\n\n        /* 超小屏幕 */\n        @media (max-width: 480px) {\n            :root {\n                --cell-size: 25px;\n                --counter-font-size: 20px;\n                --smiley-size: 35px;\n            }\n\n            body {\n                padding: 5px;\n            }\n\n            .leaderboard-panel {\n                display: none;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"main-container\">\n        <!-- 排行榜面板 -->\n        <div class=\"leaderboard-panel\" id=\"leaderboard-panel\">\n            <div class=\"leaderboard-header\">\n                <h3>🏆 排行榜</h3>\n                <div class=\"leaderboard-tabs\">\n                    <button class=\"tab-button active\" data-difficulty=\"beginner\" onclick=\"switchLeaderboardDifficulty('beginner')\">初级</button>\n                    <button class=\"tab-button\" data-difficulty=\"intermediate\" onclick=\"switchLeaderboardDifficulty('intermediate')\">中级</button>\n                    <button class=\"tab-button\" data-difficulty=\"expert\" onclick=\"switchLeaderboardDifficulty('expert')\">专家</button>\n                </div>\n            </div>\n            <div class=\"leaderboard-content\">\n                <div class=\"leaderboard-loading\" id=\"leaderboard-loading\">\n                    <div class=\"loading-spinner\"></div>\n                    <span>加载中...</span>\n                </div>\n                <div class=\"leaderboard-list\" id=\"leaderboard-list\">\n                    <!-- 动态生成排行榜项目 -->\n                </div>\n                <div class=\"leaderboard-empty\" id=\"leaderboard-empty\" style=\"display: none;\">\n                    <div class=\"empty-icon\">🎯</div>\n                    <div class=\"empty-text\">暂无记录<br>快来创造第一个记录吧！</div>\n                </div>\n            </div>\n        </div>\n\n        <!-- 游戏区域 -->\n        <div class=\"game-container\">\n            <div class=\"difficulty-selector\">\n                <div class=\"difficulty-buttons\">\n                    <button class=\"difficulty-button active\" onclick=\"setDifficulty('beginner')\">初级</button>\n                    <button class=\"difficulty-button\" onclick=\"setDifficulty('intermediate')\">中级</button>\n                    <button class=\"difficulty-button\" onclick=\"setDifficulty('expert')\">专家</button>\n                </div>\n                <div class=\"header-controls\">\n                    <button class=\"help-button\" onclick=\"showHelp()\">怎么玩</button>\n                    <button class=\"leaderboard-toggle\" id=\"leaderboard-toggle\" onclick=\"toggleLeaderboard()\" style=\"display: none;\">📊 排行榜</button>\n                </div>\n            </div>\n\n            <div class=\"game-header\">\n                <div class=\"counter\" id=\"mine-counter\">010</div>\n                <button class=\"smiley-button\" id=\"smiley-button\" onclick=\"newGame()\">😊</button>\n                <div class=\"counter\" id=\"timer\">000</div>\n            </div>\n\n            <div class=\"game-board\">\n                <div class=\"board-grid\" id=\"board-grid\"></div>\n            </div>\n        </div>\n    </div>\n\n    <!-- 游戏结束提示框 -->\n    <div class=\"game-modal\" id=\"game-modal\">\n        <div class=\"modal-content\">\n            <div class=\"modal-icon\" id=\"modal-icon\">🎉</div>\n            <div class=\"modal-title\" id=\"modal-title\">恭喜胜利！</div>\n            <div class=\"modal-message\" id=\"modal-message\">\n                用时：120秒<br>\n                难度：初级\n            </div>\n            <div class=\"modal-buttons\">\n                <button class=\"modal-button\" onclick=\"closeModal()\">确定</button>\n                <button class=\"modal-button\" onclick=\"newGameFromModal()\">新游戏</button>\n                <button class=\"modal-button primary\" id=\"upload-score-btn\" onclick=\"showUploadDialog()\" style=\"display: none;\">🏆 上传成绩</button>\n            </div>\n        </div>\n    </div>\n\n    <!-- 成绩上传对话框 -->\n    <div class=\"game-modal\" id=\"upload-modal\">\n        <div class=\"modal-content\">\n            <div class=\"modal-icon\">🏆</div>\n            <div class=\"modal-title\">上传成绩</div>\n            <div class=\"modal-message\">\n                <div class=\"upload-info\" id=\"upload-info\">\n                    用时：120秒<br>\n                    难度：初级\n                </div>\n                <div class=\"input-group\">\n                    <label for=\"username-input\">请输入您的用户名：</label>\n                    <input type=\"text\" id=\"username-input\" placeholder=\"1-20个字符\" maxlength=\"20\" autocomplete=\"off\">\n                    <div class=\"input-hint\">只能包含字母、数字、中文、下划线和连字符</div>\n                </div>\n            </div>\n            <div class=\"modal-buttons\">\n                <button class=\"modal-button\" onclick=\"closeUploadModal()\">取消</button>\n                <button class=\"modal-button primary\" onclick=\"submitScore()\">\n                    <span id=\"submit-text\">提交</span>\n                    <div class=\"button-spinner\" id=\"submit-spinner\" style=\"display: none;\"></div>\n                </button>\n            </div>\n        </div>\n    </div>\n\n    <!-- 怎么玩提示框 -->\n    <div class=\"game-modal\" id=\"help-modal\">\n        <div class=\"modal-content\">\n            <div class=\"modal-icon\">🎯</div>\n            <div class=\"modal-title\">怎么玩</div>\n            <div class=\"modal-message\" style=\"text-align: left; line-height: 1.6;\">\n                <strong>🎯 游戏目标：</strong><br>\n                找出所有地雷而不踩到它们！<br><br>\n\n                <strong>🖱️ 操作方法：</strong><br>\n                • 左键：挖掘格子<br>\n                • 右键：标记地雷<br>\n                • 双键：在已揭开的数字上同时按左右键，快速挖掘周围格子<br>\n                &nbsp;&nbsp;（当标记数等于数字时生效）<br><br>\n\n                <strong>📱 移动端：</strong><br>\n                长按格子标记地雷<br><br>\n\n                <strong>🏆 难度选择：</strong><br>\n                • 初级：9×9，10个地雷<br>\n                • 中级：16×16，40个地雷<br>\n                • 专家：30×16，99个地雷<br><br>\n\n                <strong>💡 提示：</strong><br>\n                数字表示周围8个格子中地雷的数量\n            </div>\n            <button class=\"modal-button\" onclick=\"closeHelpModal()\">知道了</button>\n        </div>\n    </div>\n\n    <script>\n        // 经典扫雷游戏逻辑\n        class MinesweeperGame {\n            constructor() {\n                this.difficulties = {\n                    beginner: { rows: 9, cols: 9, mines: 10 },\n                    intermediate: { rows: 16, cols: 16, mines: 40 },\n                    expert: { rows: 16, cols: 30, mines: 99 }\n                };\n\n                this.currentDifficulty = 'beginner';\n                this.board = [];\n                this.revealed = [];\n                this.flagged = [];\n                this.gameState = 'ready'; // ready, playing, won, lost\n                this.firstClick = true;\n                this.startTime = null;\n                this.timer = null;\n                this.mineCount = 0;\n                this.flagCount = 0;\n\n                // 双键快速挖掘状态 - 增强版本\n                this.mouseButtons = { left: false, right: false };\n                this.quickDigCell = null;\n                this.mouseDownTimestamp = { left: 0, right: 0 };\n                this.isQuickDigActive = false;\n                this.touchSupport = {\n                    longPressTimer: null,\n                    isLongPress: false,\n                    startTime: 0,\n                    threshold: 500, // 长按阈值（毫秒）\n                    lastTapTime: 0,\n                    lastTapRow: -1,\n                    lastTapCol: -1\n                };\n\n                // 性能优化：状态跟踪机制\n                this.previousCellStates = []; // 记录上次的格子状态\n                this.changedCells = new Set(); // 记录发生变化的格子\n                this.updatePending = false; // 防止重复的更新请求\n\n                this.initGame();\n            }\n\n            initGame() {\n                const config = this.difficulties[this.currentDifficulty];\n                this.rows = config.rows;\n                this.cols = config.cols;\n                this.mineCount = config.mines;\n                this.flagCount = 0;\n\n                // 初始化游戏板\n                this.board = Array(this.rows).fill().map(() => Array(this.cols).fill(0));\n                this.revealed = Array(this.rows).fill().map(() => Array(this.cols).fill(false));\n                this.flagged = Array(this.rows).fill().map(() => Array(this.cols).fill(false));\n\n                // 初始化状态跟踪\n                this.previousCellStates = Array(this.rows).fill().map(() => Array(this.cols).fill(null));\n                this.changedCells.clear();\n                this.updatePending = false;\n\n                this.gameState = 'ready';\n                this.firstClick = true;\n                this.startTime = null;\n\n                if (this.timer) {\n                    clearInterval(this.timer);\n                    this.timer = null;\n                }\n\n                this.createBoard();\n                // 首次初始化时强制全量更新\n                this.updateDisplay(true);\n\n                // 重置显示\n                document.getElementById('smiley-button').textContent = '😊';\n                document.getElementById('timer').textContent = '000';\n            }\n\n            createBoard() {\n                const boardGrid = document.getElementById('board-grid');\n\n                // 完全清除现有内容和样式\n                boardGrid.innerHTML = '';\n                boardGrid.removeAttribute('style');\n                boardGrid.className = '';\n\n                // 强制重新计算CSS变量（确保使用当前游戏尺寸）\n                this.calculateOptimalCellSize();\n\n                // 等待CSS变量更新完成\n                let cellSize;\n                let attempts = 0;\n                const maxAttempts = 10;\n\n                do {\n                    // 强制重新计算样式\n                    document.body.offsetHeight;\n                    document.documentElement.offsetHeight;\n\n                    // 获取CSS变量值\n                    cellSize = getComputedStyle(document.documentElement).getPropertyValue('--cell-size').trim();\n                    attempts++;\n\n                    // 如果获取到了有效值就跳出\n                    if (cellSize && cellSize !== '30px' && cellSize !== '') {\n                        break;\n                    }\n\n                    // 如果多次尝试失败，手动计算格子大小\n                    if (attempts >= maxAttempts) {\n                        const viewportWidth = window.innerWidth;\n                        const viewportHeight = window.innerHeight;\n                        const availableWidth = viewportWidth - 120;\n                        const availableHeight = viewportHeight - 200;\n                        const optimalSize = Math.min(\n                            Math.floor(availableWidth / this.cols),\n                            Math.floor(availableHeight / this.rows)\n                        );\n                        cellSize = Math.max(20, Math.min(45, optimalSize)) + 'px';\n                        break;\n                    }\n                } while (attempts < maxAttempts);\n\n                // 直接设置网格布局\n                boardGrid.style.display = 'grid';\n                boardGrid.style.gridTemplateColumns = 'repeat(' + this.cols + ', ' + cellSize + ')';\n                boardGrid.style.gridTemplateRows = 'repeat(' + this.rows + ', ' + cellSize + ')';\n                boardGrid.style.gap = '0';\n                boardGrid.style.border = '1px solid #808080';\n\n                // 多重强制重新渲染\n                boardGrid.offsetHeight;\n                boardGrid.offsetWidth;\n\n                // 强制重新计算布局\n                boardGrid.style.display = 'none';\n                boardGrid.offsetHeight;\n                boardGrid.style.display = 'grid';\n\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        const cell = document.createElement('div');\n                        cell.className = 'cell';\n                        cell.dataset.row = row;\n                        cell.dataset.col = col;\n\n                        cell.addEventListener('click', (e) => this.handleLeftClick(row, col, e));\n                        cell.addEventListener('contextmenu', (e) => this.handleRightClick(row, col, e));\n\n                        // 双键快速挖掘支持\n                        cell.addEventListener('mousedown', (e) => this.handleMouseDown(row, col, e));\n                        cell.addEventListener('mouseup', (e) => this.handleMouseUp(row, col, e));\n\n                        // 移动端增强触摸支持\n                        let touchTimer = null;\n                        let touchStartTime = 0;\n                        let touchMoved = false;\n\n                        cell.addEventListener('touchstart', (e) => {\n                            touchStartTime = Date.now();\n                            touchMoved = false;\n\n                            // 长按支持（标记地雷）\n                            touchTimer = setTimeout(() => {\n                                if (!touchMoved) {\n                                    this.handleRightClick(row, col, e);\n                                    // 触觉反馈（如果支持）\n                                    if (navigator.vibrate) {\n                                        navigator.vibrate(50);\n                                    }\n                                }\n                            }, 500);\n                        });\n\n                        cell.addEventListener('touchmove', (e) => {\n                            touchMoved = true;\n                            if (touchTimer) {\n                                clearTimeout(touchTimer);\n                                touchTimer = null;\n                            }\n                        });\n\n                        cell.addEventListener('touchend', (e) => {\n                            const touchDuration = Date.now() - touchStartTime;\n\n                            if (touchTimer) {\n                                clearTimeout(touchTimer);\n                                touchTimer = null;\n                            }\n\n                            // 短按处理\n                            if (!touchMoved && touchDuration < 500) {\n                                // 检查是否是双击（触摸设备的快速挖掘替代方案）\n                                if (this.touchSupport.lastTapTime &&\n                                    Date.now() - this.touchSupport.lastTapTime < 300 &&\n                                    this.touchSupport.lastTapRow === row &&\n                                    this.touchSupport.lastTapCol === col) {\n\n                                    // 双击快速挖掘\n                                    this.performQuickDig(row, col);\n                                    this.touchSupport.lastTapTime = 0; // 重置\n                                } else {\n                                    // 单击处理\n                                    this.handleLeftClick(row, col, e);\n                                    this.touchSupport.lastTapTime = Date.now();\n                                    this.touchSupport.lastTapRow = row;\n                                    this.touchSupport.lastTapCol = col;\n                                }\n                            }\n                        });\n\n                        boardGrid.appendChild(cell);\n                    }\n                }\n            }\n\n            // 计算最佳格子大小 - 确保完整适应视口，考虑排行榜占用空间\n            calculateOptimalCellSize() {\n                // 获取视口尺寸\n                const viewportWidth = window.innerWidth;\n                const viewportHeight = window.innerHeight;\n\n                // 确保使用当前游戏的行列数\n                const currentRows = this.rows || 9;\n                const currentCols = this.cols || 9;\n\n                // 动态获取排行榜面板的实际占用宽度\n                const leaderboardPanel = document.querySelector('.leaderboard-panel');\n                let leaderboardWidth = 0;\n\n                if (leaderboardPanel && window.getComputedStyle(leaderboardPanel).display !== 'none') {\n                    // 获取排行榜面板的实际宽度，包括边距和边框\n                    const rect = leaderboardPanel.getBoundingClientRect();\n                    const computedStyle = window.getComputedStyle(leaderboardPanel);\n                    const marginLeft = parseFloat(computedStyle.marginLeft) || 0;\n                    const marginRight = parseFloat(computedStyle.marginRight) || 0;\n                    leaderboardWidth = rect.width + marginLeft + marginRight;\n                }\n\n                // 精确测量实际固定元素的高度\n                const difficultySelector = document.querySelector('.difficulty-selector');\n                const gameHeader = document.querySelector('.game-header');\n                const mainContainer = document.querySelector('.main-container');\n\n                let actualFixedHeight = 70; // 默认值\n                if (difficultySelector && gameHeader) {\n                    actualFixedHeight = difficultySelector.offsetHeight + gameHeader.offsetHeight;\n\n                    // 添加容器的内边距\n                    if (mainContainer) {\n                        const containerStyle = window.getComputedStyle(mainContainer);\n                        const paddingTop = parseFloat(containerStyle.paddingTop) || 0;\n                        const paddingBottom = parseFloat(containerStyle.paddingBottom) || 0;\n                        actualFixedHeight += paddingTop + paddingBottom;\n                    }\n\n                    // 添加安全边距\n                    actualFixedHeight += 30;\n                }\n\n                // 计算游戏区域的实际可用空间\n                const gameContainer = document.querySelector('.game-container');\n                let horizontalMargins = 40; // 默认边距\n\n                if (gameContainer) {\n                    const containerStyle = window.getComputedStyle(gameContainer);\n                    const marginLeft = parseFloat(containerStyle.marginLeft) || 0;\n                    const marginRight = parseFloat(containerStyle.marginRight) || 0;\n                    const paddingLeft = parseFloat(containerStyle.paddingLeft) || 0;\n                    const paddingRight = parseFloat(containerStyle.paddingRight) || 0;\n                    horizontalMargins = marginLeft + marginRight + paddingLeft + paddingRight + 20; // 额外安全边距\n                }\n\n                const availableWidth = viewportWidth - leaderboardWidth - horizontalMargins;\n                const availableHeight = viewportHeight - actualFixedHeight;\n\n                // 边界检查 - 确保可用空间合理\n                if (availableWidth < 200 || availableHeight < 150) {\n                    console.warn('可用空间过小，使用最小布局');\n                    return this.calculateCellSizeWithWidth(\n                        Math.max(200, availableWidth),\n                        Math.max(150, availableHeight),\n                        currentRows,\n                        currentCols\n                    );\n                }\n\n                // 检查专家级是否需要隐藏排行榜\n                if (this.currentDifficulty === 'expert' && leaderboardWidth > 0 && availableWidth < 800) {\n                    this.autoHideLeaderboard();\n                    // 重新计算可用宽度（不包括排行榜）\n                    const newAvailableWidth = viewportWidth - horizontalMargins;\n                    return this.calculateCellSizeWithWidth(newAvailableWidth, availableHeight, currentRows, currentCols);\n                }\n\n                return this.calculateCellSizeWithWidth(availableWidth, availableHeight, currentRows, currentCols);\n            }\n\n            // 辅助函数：根据可用宽度计算格子大小\n            calculateCellSizeWithWidth(availableWidth, availableHeight, currentRows, currentCols) {\n                // 边界检查 - 确保输入参数合理\n                if (availableWidth <= 0 || availableHeight <= 0 || currentRows <= 0 || currentCols <= 0) {\n                    console.warn('计算格子大小时参数无效:', { availableWidth, availableHeight, currentRows, currentCols });\n                    return this.setDefaultCellSize();\n                }\n\n                // 基于可用空间计算格子大小\n                const maxCellSizeByWidth = Math.floor(availableWidth / currentCols);\n                const maxCellSizeByHeight = Math.floor(availableHeight / currentRows);\n\n                // 取较小值确保完整显示\n                let optimalSize = Math.min(maxCellSizeByWidth, maxCellSizeByHeight);\n\n                // 设置合理的大小范围\n                const minCellSize = 12;  // 最小可用大小\n                const maxCellSize = 50;  // 最大合理大小\n                optimalSize = Math.max(minCellSize, Math.min(maxCellSize, optimalSize));\n\n                // 精确验证 - 确保游戏板不会超出可用空间\n                const boardBorder = 2; // 游戏板边框\n                const totalBoardWidth = optimalSize * currentCols + boardBorder;\n                const totalBoardHeight = optimalSize * currentRows + boardBorder;\n\n                // 如果总尺寸超出可用空间，进一步缩小\n                if (totalBoardWidth > availableWidth || totalBoardHeight > availableHeight) {\n                    const scaleX = availableWidth / totalBoardWidth;\n                    const scaleY = availableHeight / totalBoardHeight;\n                    const scale = Math.min(scaleX, scaleY) * 0.95; // 留5%安全边距\n                    optimalSize = Math.floor(optimalSize * scale);\n                    optimalSize = Math.max(minCellSize, optimalSize);\n                }\n\n                // 最终边界检查\n                if (optimalSize < minCellSize) {\n                    console.warn('计算出的格子大小过小，使用默认值');\n                    return this.setDefaultCellSize();\n                }\n\n                // 批量更新CSS变量（避免多次重排）\n                this.updateCSSVariables(optimalSize);\n\n                // 输出调试信息（仅在开发模式下）\n                if (console.log) {\n                    console.log('布局计算结果:');\n                    console.log('  视口尺寸: ' + window.innerWidth + 'x' + window.innerHeight + 'px');\n                    console.log('  可用空间: ' + availableWidth + 'x' + availableHeight + 'px');\n                    console.log('  游戏尺寸: ' + currentCols + 'x' + currentRows);\n                    console.log('  格子大小: ' + optimalSize + 'px');\n                    console.log('  游戏板尺寸: ' + totalBoardWidth + 'x' + totalBoardHeight + 'px');\n                }\n\n                return optimalSize;\n            }\n\n            // 设置默认格子大小\n            setDefaultCellSize() {\n                const defaultSize = 25;\n                this.updateCSSVariables(defaultSize);\n                return defaultSize;\n            }\n\n            // 批量更新CSS变量，避免多次重排\n            updateCSSVariables(cellSize) {\n                const root = document.documentElement;\n\n                // 计算相关尺寸\n                const counterFontSize = Math.max(12, Math.floor(cellSize * 0.6));\n                const smileySize = Math.max(24, Math.floor(cellSize * 1.0));\n\n                // 批量设置CSS变量\n                root.style.setProperty('--cell-size', cellSize + 'px');\n                root.style.setProperty('--counter-font-size', counterFontSize + 'px');\n                root.style.setProperty('--smiley-size', smileySize + 'px');\n\n                // 使用requestAnimationFrame优化重排时机\n                if (window.requestAnimationFrame) {\n                    requestAnimationFrame(() => {\n                        // 触发一次重排以应用新的CSS变量\n                        root.offsetHeight;\n                    });\n                } else {\n                    // 降级处理\n                    root.offsetHeight;\n                }\n            }\n\n            // 自动隐藏排行榜（当空间不足时）\n            autoHideLeaderboard() {\n                const leaderboardPanel = document.querySelector('.leaderboard-panel');\n                const leaderboardToggle = document.querySelector('.leaderboard-toggle');\n\n                if (leaderboardPanel) {\n                    leaderboardPanel.style.display = 'none';\n                    leaderboardPanel.classList.add('auto-hidden');\n                }\n\n                if (leaderboardToggle) {\n                    leaderboardToggle.style.display = 'inline-block';\n                }\n            }\n\n            // 生成地雷 - Fisher-Yates洗牌算法\n            generateMines(firstClickRow, firstClickCol) {\n                const positions = [];\n\n                // 创建所有可能的位置（除了首次点击位置及其周围）\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        // 跳过首次点击位置及其周围8个格子\n                        if (Math.abs(row - firstClickRow) <= 1 && Math.abs(col - firstClickCol) <= 1) {\n                            continue;\n                        }\n                        positions.push([row, col]);\n                    }\n                }\n\n                // Fisher-Yates洗牌\n                for (let i = positions.length - 1; i > 0; i--) {\n                    const j = Math.floor(Math.random() * (i + 1));\n                    [positions[i], positions[j]] = [positions[j], positions[i]];\n                }\n\n                // 放置地雷\n                for (let i = 0; i < this.mineCount && i < positions.length; i++) {\n                    const [row, col] = positions[i];\n                    this.board[row][col] = -1; // -1 表示地雷\n                }\n\n                // 计算数字\n                this.calculateNumbers();\n            }\n\n            // 计算每个格子周围的地雷数\n            calculateNumbers() {\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        if (this.board[row][col] !== -1) {\n                            let count = 0;\n                            for (let dr = -1; dr <= 1; dr++) {\n                                for (let dc = -1; dc <= 1; dc++) {\n                                    const newRow = row + dr;\n                                    const newCol = col + dc;\n                                    if (this.isValidCell(newRow, newCol) && this.board[newRow][newCol] === -1) {\n                                        count++;\n                                    }\n                                }\n                            }\n                            this.board[row][col] = count;\n                        }\n                    }\n                }\n            }\n\n            // 检查坐标是否有效\n            isValidCell(row, col) {\n                return row >= 0 && row < this.rows && col >= 0 && col < this.cols;\n            }\n\n            // 左键点击处理\n            handleLeftClick(row, col, event) {\n                event.preventDefault();\n\n                if (this.gameState === 'won' || this.gameState === 'lost') return;\n                if (this.flagged[row][col]) return;\n\n                // 首次点击\n                if (this.firstClick) {\n                    this.generateMines(row, col);\n                    this.firstClick = false;\n                    this.gameState = 'playing';\n                    this.startTimer();\n                    document.getElementById('smiley-button').textContent = '😊';\n                }\n\n                this.revealCell(row, col);\n                this.updateDisplay();\n                this.checkGameState();\n            }\n\n            // 右键点击处理\n            handleRightClick(row, col, event) {\n                event.preventDefault();\n\n                if (this.gameState === 'won' || this.gameState === 'lost') return;\n                if (this.revealed[row][col]) return;\n\n                this.flagged[row][col] = !this.flagged[row][col];\n                this.flagCount += this.flagged[row][col] ? 1 : -1;\n\n                this.updateDisplay();\n            }\n\n            // 鼠标按下处理（用于双键快速挖掘）- 增强版本\n            handleMouseDown(row, col, event) {\n                if (this.gameState === 'won' || this.gameState === 'lost') return;\n\n                // 阻止默认行为，避免与其他事件冲突\n                event.preventDefault();\n\n                const currentTime = Date.now();\n\n                if (event.button === 0) { // 左键\n                    this.mouseButtons.left = true;\n                    this.mouseDownTimestamp.left = currentTime;\n                } else if (event.button === 2) { // 右键\n                    this.mouseButtons.right = true;\n                    this.mouseDownTimestamp.right = currentTime;\n                }\n\n                // 检查是否双键按下（增加时间戳验证）\n                if (this.mouseButtons.left && this.mouseButtons.right) {\n                    // 验证两个按键的时间戳差异不超过200ms\n                    const timeDiff = Math.abs(this.mouseDownTimestamp.left - this.mouseDownTimestamp.right);\n                    if (timeDiff <= 200) {\n                        this.isQuickDigActive = true;\n                        this.quickDigCell = { row, col, timestamp: currentTime };\n                        this.highlightQuickDigArea(row, col, true);\n\n                        // 添加视觉反馈\n                        this.addQuickDigFeedback(row, col);\n                    }\n                }\n            }\n\n            // 鼠标释放处理 - 增强版本\n            handleMouseUp(row, col, event) {\n                if (this.gameState === 'won' || this.gameState === 'lost') return;\n\n                // 阻止默认行为\n                event.preventDefault();\n\n                const currentTime = Date.now();\n                const wasQuickDigActive = this.isQuickDigActive;\n\n                // 更新鼠标按钮状态\n                if (event.button === 0) { // 左键释放\n                    this.mouseButtons.left = false;\n                    this.mouseDownTimestamp.left = 0;\n                } else if (event.button === 2) { // 右键释放\n                    this.mouseButtons.right = false;\n                    this.mouseDownTimestamp.right = 0;\n                }\n\n                // 执行快速挖掘（增强验证）\n                if (wasQuickDigActive && this.quickDigCell &&\n                    this.quickDigCell.row === row && this.quickDigCell.col === col) {\n\n                    // 验证快速挖掘的有效性\n                    const timeSinceStart = currentTime - this.quickDigCell.timestamp;\n                    if (timeSinceStart <= 2000) { // 2秒内有效\n                        this.performQuickDig(row, col);\n                    }\n                }\n\n                // 清理快速挖掘状态\n                this.cleanupQuickDigState();\n            }\n\n            // 高亮快速挖掘区域 - 增强版本\n            highlightQuickDigArea(row, col, highlight) {\n                for (let dr = -1; dr <= 1; dr++) {\n                    for (let dc = -1; dc <= 1; dc++) {\n                        const newRow = row + dr;\n                        const newCol = col + dc;\n                        if (this.isValidCell(newRow, newCol) && !this.revealed[newRow][newCol]) {\n                            const cellIndex = newRow * this.cols + newCol;\n                            const cellElement = document.querySelectorAll('.cell')[cellIndex];\n                            if (cellElement) {\n                                if (highlight) {\n                                    cellElement.classList.add('quick-dig-highlight');\n                                } else {\n                                    cellElement.classList.remove('quick-dig-highlight');\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n\n            // 添加快速挖掘视觉反馈\n            addQuickDigFeedback(row, col) {\n                const cellIndex = row * this.cols + col;\n                const cellElement = document.querySelectorAll('.cell')[cellIndex];\n                if (cellElement) {\n                    cellElement.classList.add('quick-dig-active');\n                    // 短暂的视觉反馈\n                    setTimeout(() => {\n                        if (cellElement) {\n                            cellElement.classList.remove('quick-dig-active');\n                        }\n                    }, 150);\n                }\n            }\n\n            // 清理快速挖掘状态\n            cleanupQuickDigState() {\n                if (this.quickDigCell) {\n                    this.highlightQuickDigArea(this.quickDigCell.row, this.quickDigCell.col, false);\n                    this.quickDigCell = null;\n                }\n                this.isQuickDigActive = false;\n\n                // 清理所有可能残留的高亮状态\n                document.querySelectorAll('.cell').forEach(cell => {\n                    cell.classList.remove('quick-dig-highlight', 'quick-dig-active');\n                });\n            }\n\n            // 全局状态重置（用于处理意外情况）\n            resetMouseState() {\n                this.mouseButtons.left = false;\n                this.mouseButtons.right = false;\n                this.mouseDownTimestamp.left = 0;\n                this.mouseDownTimestamp.right = 0;\n                this.cleanupQuickDigState();\n            }\n\n            // 执行快速挖掘\n            performQuickDig(row, col) {\n                // 只有已揭开的数字格子才能进行快速挖掘\n                if (!this.revealed[row][col] || this.board[row][col] <= 0) {\n                    return;\n                }\n\n                const targetNumber = this.board[row][col];\n                let flaggedCount = 0;\n\n                // 计算周围标记的地雷数\n                for (let dr = -1; dr <= 1; dr++) {\n                    for (let dc = -1; dc <= 1; dc++) {\n                        const newRow = row + dr;\n                        const newCol = col + dc;\n                        if (this.isValidCell(newRow, newCol) && this.flagged[newRow][newCol]) {\n                            flaggedCount++;\n                        }\n                    }\n                }\n\n                // 只有当标记数等于目标数字时才能快速挖掘\n                if (flaggedCount === targetNumber) {\n                    for (let dr = -1; dr <= 1; dr++) {\n                        for (let dc = -1; dc <= 1; dc++) {\n                            const newRow = row + dr;\n                            const newCol = col + dc;\n                            if (this.isValidCell(newRow, newCol) &&\n                                !this.revealed[newRow][newCol] &&\n                                !this.flagged[newRow][newCol]) {\n                                this.revealCell(newRow, newCol);\n                            }\n                        }\n                    }\n                    this.updateDisplay();\n                    this.checkGameState();\n                }\n            }\n\n            // 揭开格子\n            revealCell(row, col) {\n                if (!this.isValidCell(row, col) || this.revealed[row][col] || this.flagged[row][col]) {\n                    return;\n                }\n\n                this.revealed[row][col] = true;\n\n                // 如果是地雷，游戏结束\n                if (this.board[row][col] === -1) {\n                    this.gameState = 'lost';\n                    this.revealAllMines();\n                    return;\n                }\n\n                // 如果是空格子（数字为0），自动展开周围\n                if (this.board[row][col] === 0) {\n                    for (let dr = -1; dr <= 1; dr++) {\n                        for (let dc = -1; dc <= 1; dc++) {\n                            this.revealCell(row + dr, col + dc);\n                        }\n                    }\n                }\n            }\n\n            // 揭开所有地雷（游戏失败时）\n            revealAllMines() {\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        if (this.board[row][col] === -1) {\n                            this.revealed[row][col] = true;\n                        }\n                    }\n                }\n            }\n\n            // 检查游戏状态\n            checkGameState() {\n                if (this.gameState === 'lost') {\n                    document.getElementById('smiley-button').textContent = '😵';\n                    this.stopTimer();\n\n                    // 显示失败提示\n                    setTimeout(() => {\n                        this.showGameModal('💣', '游戏失败！', '踩到地雷了！\\\\n点击笑脸或新游戏按钮重新开始。');\n                    }, 500);\n                    return;\n                }\n\n                // 检查是否胜利\n                let unrevealedCount = 0;\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        if (!this.revealed[row][col] && this.board[row][col] !== -1) {\n                            unrevealedCount++;\n                        }\n                    }\n                }\n\n                if (unrevealedCount === 0) {\n                    this.gameState = 'won';\n                    document.getElementById('smiley-button').textContent = '😎';\n                    this.stopTimer();\n\n                    // 自动标记所有地雷\n                    for (let row = 0; row < this.rows; row++) {\n                        for (let col = 0; col < this.cols; col++) {\n                            if (this.board[row][col] === -1 && !this.flagged[row][col]) {\n                                this.flagged[row][col] = true;\n                                this.flagCount++;\n                            }\n                        }\n                    }\n\n                    // 显示胜利提示\n                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);\n                    setTimeout(() => {\n                        const message = '用时：' + elapsed + '秒\\\\n难度：' + this.getDifficultyName() + '\\\\n\\\\n太棒了！你成功找出了所有地雷！';\n                        this.showGameModal('🎉', '恭喜胜利！', message, elapsed, this.currentDifficulty);\n                    }, 500);\n                }\n            }\n\n            // 获取难度名称\n            getDifficultyName() {\n                const names = {\n                    'beginner': '初级',\n                    'intermediate': '中级',\n                    'expert': '专家'\n                };\n                return names[this.currentDifficulty] || '自定义';\n            }\n\n            // 显示游戏结束提示框\n            showGameModal(icon, title, message, time = null, difficulty = null) {\n                const modal = document.getElementById('game-modal');\n                const uploadBtn = document.getElementById('upload-score-btn');\n\n                document.getElementById('modal-icon').textContent = icon;\n                document.getElementById('modal-title').textContent = title;\n                document.getElementById('modal-message').innerHTML = message.replace(/\\\\n/g, '<br>');\n\n                // 如果是胜利且有时间和难度，显示上传按钮\n                if (time !== null && difficulty !== null) {\n                    uploadBtn.style.display = 'inline-block';\n                    // 保存成绩信息供上传使用\n                    window.currentScore = { time, difficulty };\n                } else {\n                    uploadBtn.style.display = 'none';\n                    window.currentScore = null;\n                }\n\n                modal.style.display = 'flex';\n                setTimeout(() => {\n                    modal.classList.add('show');\n                }, 10);\n            }\n\n            // 开始计时器\n            startTimer() {\n                this.startTime = Date.now();\n                this.timer = setInterval(() => {\n                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);\n                    const displayTime = Math.min(elapsed, 999);\n                    document.getElementById('timer').textContent = displayTime.toString().padStart(3, '0');\n                }, 1000);\n            }\n\n            // 停止计时器\n            stopTimer() {\n                if (this.timer) {\n                    clearInterval(this.timer);\n                    this.timer = null;\n                }\n            }\n\n            // 更新显示 - 优化版本，支持增量更新\n            updateDisplay(forceFullUpdate = false) {\n                // 更新地雷计数器\n                const remainingMines = this.mineCount - this.flagCount;\n                document.getElementById('mine-counter').textContent =\n                    Math.max(-99, Math.min(999, remainingMines)).toString().padStart(3, '0');\n\n                // 防止重复的更新请求\n                if (this.updatePending && !forceFullUpdate) {\n                    return;\n                }\n\n                this.updatePending = true;\n\n                // 使用requestAnimationFrame优化更新时机\n                requestAnimationFrame(() => {\n                    this.performDisplayUpdate(forceFullUpdate);\n                    this.updatePending = false;\n                });\n            }\n\n            // 执行实际的显示更新\n            performDisplayUpdate(forceFullUpdate = false) {\n                const cells = document.querySelectorAll('.cell');\n\n                if (forceFullUpdate || this.previousCellStates.length === 0) {\n                    // 全量更新（首次或强制更新）\n                    this.performFullUpdate(cells);\n                } else {\n                    // 增量更新（只更新变化的格子）\n                    this.performIncrementalUpdate(cells);\n                }\n\n                // 更新状态跟踪\n                this.updateStateTracking();\n            }\n\n            // 执行全量更新\n            performFullUpdate(cells) {\n                cells.forEach((cell, index) => {\n                    const row = Math.floor(index / this.cols);\n                    const col = index % this.cols;\n                    this.updateSingleCell(cell, row, col);\n                });\n            }\n\n            // 执行增量更新\n            performIncrementalUpdate(cells) {\n                // 检测变化的格子\n                this.detectChangedCells();\n\n                // 只更新变化的格子\n                this.changedCells.forEach(cellIndex => {\n                    const row = Math.floor(cellIndex / this.cols);\n                    const col = cellIndex % this.cols;\n                    const cell = cells[cellIndex];\n                    if (cell) {\n                        this.updateSingleCell(cell, row, col);\n                    }\n                });\n\n                // 清除变化记录\n                this.changedCells.clear();\n            }\n\n            // 检测发生变化的格子\n            detectChangedCells() {\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        const currentState = this.getCellState(row, col);\n                        const previousState = this.previousCellStates[row][col];\n\n                        if (currentState !== previousState) {\n                            const cellIndex = row * this.cols + col;\n                            this.changedCells.add(cellIndex);\n                        }\n                    }\n                }\n            }\n\n            // 获取格子的当前状态（用于比较）\n            getCellState(row, col) {\n                if (this.flagged[row][col]) {\n                    return 'flagged';\n                } else if (this.revealed[row][col]) {\n                    if (this.board[row][col] === -1) {\n                        return 'mine';\n                    } else if (this.board[row][col] > 0) {\n                        return 'number-' + this.board[row][col];\n                    } else {\n                        return 'revealed-empty';\n                    }\n                } else {\n                    return 'hidden';\n                }\n            }\n\n            // 更新单个格子的显示\n            updateSingleCell(cell, row, col) {\n                // 清除所有状态类\n                cell.className = 'cell';\n                cell.textContent = '';\n\n                if (this.flagged[row][col]) {\n                    cell.classList.add('flagged');\n                } else if (this.revealed[row][col]) {\n                    cell.classList.add('revealed');\n\n                    if (this.board[row][col] === -1) {\n                        cell.classList.add('mine');\n                        cell.textContent = '💣';\n                    } else if (this.board[row][col] > 0) {\n                        cell.classList.add('number-' + this.board[row][col]);\n                        cell.textContent = this.board[row][col];\n                    }\n                }\n            }\n\n            // 更新状态跟踪\n            updateStateTracking() {\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        this.previousCellStates[row][col] = this.getCellState(row, col);\n                    }\n                }\n            }\n        }\n\n        // 排行榜管理器 - 增强版本\n        class LeaderboardManager {\n            constructor(game) {\n                this.game = game;\n                this.currentDifficulty = 'beginner';\n\n                // 增强的缓存系统\n                this.cache = {}; // 缓存数据\n                this.cacheTimestamps = {}; // 缓存时间戳\n                this.cacheVersions = {}; // 缓存版本控制\n                this.cacheExpiry = 5 * 60 * 1000; // 5分钟过期时间\n\n                // 请求状态管理\n                this.isLoading = false;\n                this.loadingPromises = {}; // 防止重复请求\n                this.retryAttempts = {}; // 重试计数\n                this.maxRetries = 3; // 最大重试次数\n\n                // 网络状态检测\n                this.isOnline = navigator.onLine;\n                this.networkRetryDelay = 1000; // 网络重试延迟（毫秒）\n\n                // 离线模式支持\n                this.offlineData = {}; // 离线数据存储\n                this.pendingUploads = []; // 待上传队列\n\n                // 初始化网络状态监听\n                this.initNetworkListeners();\n            }\n\n            // 初始化网络状态监听器\n            initNetworkListeners() {\n                window.addEventListener('online', () => {\n                    this.isOnline = true;\n                    console.log('网络连接已恢复');\n                    this.processPendingUploads();\n                });\n\n                window.addEventListener('offline', () => {\n                    this.isOnline = false;\n                    console.log('网络连接已断开，进入离线模式');\n                });\n            }\n\n            // 检查缓存是否有效\n            isCacheValid(difficulty) {\n                if (!this.cache[difficulty] || !this.cacheTimestamps[difficulty]) {\n                    return false;\n                }\n\n                const now = Date.now();\n                const cacheAge = now - this.cacheTimestamps[difficulty];\n                return cacheAge < this.cacheExpiry;\n            }\n\n            // 更新缓存\n            updateCache(difficulty, data, version = null) {\n                this.cache[difficulty] = data;\n                this.cacheTimestamps[difficulty] = Date.now();\n                this.cacheVersions[difficulty] = version || Date.now();\n\n                // 同时更新离线数据\n                this.offlineData[difficulty] = {\n                    data: data,\n                    timestamp: Date.now(),\n                    version: this.cacheVersions[difficulty]\n                };\n            }\n\n            // 获取缓存数据（包括离线数据）\n            getCachedData(difficulty) {\n                // 优先使用在线缓存\n                if (this.isCacheValid(difficulty)) {\n                    return this.cache[difficulty];\n                }\n\n                // 如果离线，使用离线数据\n                if (!this.isOnline && this.offlineData[difficulty]) {\n                    return this.offlineData[difficulty].data;\n                }\n\n                return null;\n            }\n\n            // 指数退避重试机制\n            async retryWithBackoff(fn, difficulty, attempt = 0) {\n                try {\n                    return await fn();\n                } catch (error) {\n                    if (attempt >= this.maxRetries) {\n                        throw error;\n                    }\n\n                    const delay = Math.min(1000 * Math.pow(2, attempt), 10000); // 最大10秒\n                    console.log('重试第 ' + (attempt + 1) + ' 次，延迟 ' + delay + 'ms');\n\n                    await new Promise(resolve => setTimeout(resolve, delay));\n                    return this.retryWithBackoff(fn, difficulty, attempt + 1);\n                }\n            }\n\n            async loadLeaderboard(difficulty) {\n                // 防止重复请求\n                if (this.loadingPromises[difficulty]) {\n                    return this.loadingPromises[difficulty];\n                }\n\n                // 检查缓存\n                const cachedData = this.getCachedData(difficulty);\n                if (cachedData) {\n                    this.updateDisplay(cachedData);\n                    return;\n                }\n\n                // 如果离线且没有缓存数据\n                if (!this.isOnline) {\n                    this.showError('网络连接不可用，请检查网络设置');\n                    return;\n                }\n\n                this.isLoading = true;\n                this.showLoading();\n\n                // 创建加载Promise\n                this.loadingPromises[difficulty] = this.performLoadLeaderboard(difficulty);\n\n                try {\n                    await this.loadingPromises[difficulty];\n                } finally {\n                    delete this.loadingPromises[difficulty];\n                    this.hideLoading();\n                    this.isLoading = false;\n                }\n            }\n\n            // 执行实际的排行榜加载\n            async performLoadLeaderboard(difficulty) {\n                const loadFunction = async () => {\n                    const response = await fetch('/api/leaderboard/' + difficulty, {\n                        method: 'GET',\n                        headers: {\n                            'Cache-Control': 'no-cache',\n                            'Content-Type': 'application/json'\n                        }\n                    });\n\n                    if (!response.ok) {\n                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);\n                    }\n\n                    const result = await response.json();\n\n                    if (!result.success) {\n                        throw new Error(result.error || '服务器返回错误');\n                    }\n\n                    return result;\n                };\n\n                try {\n                    const result = await this.retryWithBackoff(loadFunction, difficulty);\n\n                    // 更新缓存和显示\n                    this.updateCache(difficulty, result.data, result.version);\n                    this.updateDisplay(result.data);\n\n                    // 重置重试计数\n                    this.retryAttempts[difficulty] = 0;\n\n                } catch (error) {\n                    console.error('加载排行榜失败:', error);\n                    this.handleLoadError(difficulty, error);\n                }\n            }\n\n            // 处理加载错误\n            handleLoadError(difficulty, error) {\n                let errorMessage = '加载排行榜失败';\n\n                if (error.message.includes('HTTP 404')) {\n                    errorMessage = '排行榜数据不存在';\n                } else if (error.message.includes('HTTP 500')) {\n                    errorMessage = '服务器内部错误，请稍后重试';\n                } else if (error.message.includes('Failed to fetch')) {\n                    errorMessage = '网络连接失败，请检查网络设置';\n                } else if (error.message.includes('timeout')) {\n                    errorMessage = '请求超时，请稍后重试';\n                } else {\n                    errorMessage = '加载失败: ' + error.message;\n                }\n\n                // 尝试显示离线数据\n                const offlineData = this.offlineData[difficulty];\n                if (offlineData) {\n                    this.updateDisplay(offlineData.data);\n                    this.showOfflineNotice();\n                } else {\n                    this.showError(errorMessage);\n                }\n            }\n\n            async uploadScore(username, time, difficulty) {\n                const scoreData = { username, time, difficulty, timestamp: Date.now() };\n\n                // 如果离线，添加到待上传队列\n                if (!this.isOnline) {\n                    this.pendingUploads.push(scoreData);\n                    return {\n                        success: false,\n                        error: '网络连接不可用，成绩已保存，将在网络恢复后自动上传'\n                    };\n                }\n\n                const uploadFunction = async () => {\n                    const response = await fetch('/api/leaderboard/' + difficulty, {\n                        method: 'POST',\n                        headers: {\n                            'Content-Type': 'application/json',\n                            'Cache-Control': 'no-cache'\n                        },\n                        body: JSON.stringify({\n                            username,\n                            time,\n                            clientTimestamp: Date.now(),\n                            version: this.cacheVersions[difficulty] || 0\n                        })\n                    });\n\n                    if (!response.ok) {\n                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);\n                    }\n\n                    const result = await response.json();\n\n                    if (!result.success) {\n                        throw new Error(result.error || '上传失败');\n                    }\n\n                    return result;\n                };\n\n                try {\n                    const result = await this.retryWithBackoff(uploadFunction, difficulty);\n\n                    // 更新缓存和显示\n                    this.updateCache(difficulty, result.data, result.version);\n                    if (this.currentDifficulty === difficulty) {\n                        this.updateDisplay(result.data, username);\n                    }\n\n                    return { success: true };\n\n                } catch (error) {\n                    console.error('上传成绩失败:', error);\n\n                    // 根据错误类型返回不同的错误信息\n                    let errorMessage = '上传失败';\n\n                    if (error.message.includes('HTTP 409')) {\n                        errorMessage = '数据冲突，请刷新排行榜后重试';\n                    } else if (error.message.includes('HTTP 400')) {\n                        errorMessage = '数据格式错误，请检查用户名和成绩';\n                    } else if (error.message.includes('HTTP 429')) {\n                        errorMessage = '请求过于频繁，请稍后重试';\n                    } else if (error.message.includes('Failed to fetch')) {\n                        // 网络错误，添加到待上传队列\n                        this.pendingUploads.push(scoreData);\n                        errorMessage = '网络连接失败，成绩已保存，将在网络恢复后自动上传';\n                    } else {\n                        errorMessage = '上传失败: ' + error.message;\n                    }\n\n                    return { success: false, error: errorMessage };\n                }\n            }\n\n            switchDifficulty(difficulty) {\n                this.currentDifficulty = difficulty;\n\n                // 更新标签页状态\n                document.querySelectorAll('.tab-button').forEach(btn => {\n                    btn.classList.remove('active');\n                    if (btn.dataset.difficulty === difficulty) {\n                        btn.classList.add('active');\n                    }\n                });\n\n                // 加载对应难度的排行榜\n                this.loadLeaderboard(difficulty);\n            }\n\n            updateDisplay(data, highlightUsername = null) {\n                const listElement = document.getElementById('leaderboard-list');\n                const emptyElement = document.getElementById('leaderboard-empty');\n\n                if (data.length === 0) {\n                    listElement.style.display = 'none';\n                    emptyElement.style.display = 'block';\n                    return;\n                }\n\n                listElement.style.display = 'block';\n                emptyElement.style.display = 'none';\n\n                listElement.innerHTML = data.map((record, index) => {\n                    const rank = index + 1;\n                    const rankClass = rank === 1 ? 'gold' : rank === 2 ? 'silver' : rank === 3 ? 'bronze' : '';\n                    const highlightClass = highlightUsername && record.username === highlightUsername ? 'highlight' : '';\n\n                    return '<div class=\"leaderboard-item ' + highlightClass + '\"><div class=\"leaderboard-rank ' + rankClass + '\">' + rank + '</div><div class=\"leaderboard-username\">' + this.escapeHtml(record.username) + '</div><div class=\"leaderboard-time\">' + record.time + 's</div></div>';\n                }).join('');\n            }\n\n            showLoading() {\n                document.getElementById('leaderboard-loading').style.display = 'flex';\n                document.getElementById('leaderboard-list').style.display = 'none';\n                document.getElementById('leaderboard-empty').style.display = 'none';\n            }\n\n            hideLoading() {\n                document.getElementById('leaderboard-loading').style.display = 'none';\n            }\n\n            // 处理待上传队列\n            async processPendingUploads() {\n                if (this.pendingUploads.length === 0) return;\n\n                console.log('开始处理 ' + this.pendingUploads.length + ' 个待上传成绩');\n\n                const uploads = [...this.pendingUploads];\n                this.pendingUploads = [];\n\n                for (const scoreData of uploads) {\n                    try {\n                        const result = await this.uploadScore(\n                            scoreData.username,\n                            scoreData.time,\n                            scoreData.difficulty\n                        );\n\n                        if (result.success) {\n                            console.log('成功上传成绩: ' + scoreData.username + ' - ' + scoreData.time + 's');\n                        } else {\n                            // 如果上传失败，重新加入队列\n                            this.pendingUploads.push(scoreData);\n                        }\n                    } catch (error) {\n                        console.error('处理待上传成绩失败:', error);\n                        // 重新加入队列\n                        this.pendingUploads.push(scoreData);\n                    }\n                }\n            }\n\n            // 显示离线提示\n            showOfflineNotice() {\n                const listElement = document.getElementById('leaderboard-list');\n                listElement.innerHTML =\n                    '<div style=\"text-align: center; color: #666; padding: 20px; border: 1px dashed #ccc; margin: 10px 0;\">' +\n                        '<div style=\"font-size: 24px; margin-bottom: 10px;\">📱</div>' +\n                        '<div style=\"font-weight: bold; margin-bottom: 5px;\">离线模式</div>' +\n                        '<div style=\"font-size: 12px;\">显示的是缓存数据，网络恢复后将自动更新</div>' +\n                    '</div>';\n                listElement.style.display = 'block';\n            }\n\n            showError(message) {\n                const listElement = document.getElementById('leaderboard-list');\n                listElement.innerHTML =\n                    '<div style=\"text-align: center; color: #d00; padding: 20px;\">' +\n                        '<div style=\"font-size: 24px; margin-bottom: 10px;\">⚠️</div>' +\n                        '<div style=\"font-weight: bold; margin-bottom: 5px;\">加载失败</div>' +\n                        '<div style=\"font-size: 12px;\">' + message + '</div>' +\n                        '<button onclick=\"window.leaderboardManager.loadLeaderboard(\\'' + this.currentDifficulty + '\\')\" ' +\n                                'style=\"margin-top: 10px; padding: 5px 10px; background: #c0c0c0; border: 1px outset #c0c0c0; cursor: pointer;\">' +\n                            '重试' +\n                        '</button>' +\n                    '</div>';\n                listElement.style.display = 'block';\n            }\n\n            escapeHtml(text) {\n                const div = document.createElement('div');\n                div.textContent = text;\n                return div.innerHTML;\n            }\n\n            // 清除指定难度的缓存 - 增强版本\n            clearCache(difficulty) {\n                if (difficulty) {\n                    delete this.cache[difficulty];\n                    delete this.cacheTimestamps[difficulty];\n                    delete this.cacheVersions[difficulty];\n                    delete this.offlineData[difficulty];\n                } else {\n                    this.cache = {};\n                    this.cacheTimestamps = {};\n                    this.cacheVersions = {};\n                    this.offlineData = {};\n                }\n            }\n\n            // 获取缓存状态信息\n            getCacheStatus() {\n                const status = {};\n                for (const difficulty in this.cache) {\n                    const isValid = this.isCacheValid(difficulty);\n                    const age = Date.now() - (this.cacheTimestamps[difficulty] || 0);\n                    status[difficulty] = {\n                        valid: isValid,\n                        age: Math.floor(age / 1000), // 秒\n                        version: this.cacheVersions[difficulty],\n                        hasOfflineData: !!this.offlineData[difficulty]\n                    };\n                }\n                return status;\n            }\n\n            // 强制刷新排行榜\n            async forceRefresh(difficulty) {\n                this.clearCache(difficulty);\n                await this.loadLeaderboard(difficulty);\n            }\n\n            // 获取网络状态和统计信息\n            getNetworkStatus() {\n                return {\n                    isOnline: this.isOnline,\n                    pendingUploads: this.pendingUploads.length,\n                    retryAttempts: { ...this.retryAttempts },\n                    cacheStatus: this.getCacheStatus()\n                };\n            }\n        }\n\n        // 全局游戏实例\n        let game = new MinesweeperGame();\n\n        // 全局排行榜管理器\n        window.leaderboardManager = new LeaderboardManager(game);\n\n        // 全局事件监听器 - 处理意外情况和状态重置\n        document.addEventListener('mouseup', (e) => {\n            // 只在鼠标离开游戏区域时重置状态\n            if (!e.target.closest('#board-grid')) {\n                game.resetMouseState();\n            }\n        });\n\n        // 处理鼠标离开游戏区域的情况\n        document.addEventListener('mouseleave', () => {\n            game.resetMouseState();\n        });\n\n        // 处理窗口失去焦点的情况\n        window.addEventListener('blur', () => {\n            game.resetMouseState();\n        });\n\n        // 防止右键菜单\n        document.addEventListener('contextmenu', (e) => {\n            if (e.target.classList.contains('cell')) {\n                e.preventDefault();\n            }\n        });\n\n        // 窗口大小变化时重新计算布局\n        window.addEventListener('resize', () => {\n            if (game) {\n                game.calculateOptimalCellSize();\n            }\n        });\n\n        // 点击提示框背景关闭\n        document.getElementById('game-modal').addEventListener('click', (e) => {\n            if (e.target.id === 'game-modal') {\n                closeModal();\n            }\n        });\n\n        // 点击帮助提示框背景关闭\n        document.getElementById('help-modal').addEventListener('click', (e) => {\n            if (e.target.id === 'help-modal') {\n                closeHelpModal();\n            }\n        });\n        \n        // 工具函数\n\n        // 防抖函数 - 优化窗口大小变化事件处理\n        function debounce(func, wait, immediate = false) {\n            let timeout;\n            return function executedFunction(...args) {\n                const later = () => {\n                    timeout = null;\n                    if (!immediate) func.apply(this, args);\n                };\n                const callNow = immediate && !timeout;\n                clearTimeout(timeout);\n                timeout = setTimeout(later, wait);\n                if (callNow) func.apply(this, args);\n            };\n        }\n\n        // 节流函数 - 限制函数执行频率\n        function throttle(func, limit) {\n            let inThrottle;\n            return function(...args) {\n                if (!inThrottle) {\n                    func.apply(this, args);\n                    inThrottle = true;\n                    setTimeout(() => inThrottle = false, limit);\n                }\n            };\n        }\n\n        // 窗口大小变化处理函数\n        function handleWindowResize() {\n            if (game && game.calculateOptimalCellSize) {\n                console.log('窗口大小变化，重新计算布局');\n                game.calculateOptimalCellSize();\n            }\n        }\n\n        // 创建防抖版本的resize处理函数\n        const debouncedResize = debounce(handleWindowResize, 250);\n\n        // 创建节流版本的resize处理函数（用于即时反馈）\n        const throttledResize = throttle(() => {\n            // 立即更新排行榜显示状态，提供即时反馈\n            updateLayoutForDifficulty(game.currentDifficulty);\n        }, 100);\n\n        function setDifficulty(difficulty) {\n            // 更新按钮状态\n            document.querySelectorAll('.difficulty-button').forEach(btn => {\n                btn.classList.remove('active');\n            });\n            event.target.classList.add('active');\n\n            // 先更新难度\n            game.currentDifficulty = difficulty;\n\n            // 同步切换排行榜难度\n            if (window.leaderboardManager) {\n                window.leaderboardManager.switchDifficulty(difficulty);\n            }\n\n            // 获取新难度的配置\n            const config = game.difficulties[difficulty];\n\n            // 立即更新游戏尺寸\n            game.rows = config.rows;\n            game.cols = config.cols;\n            game.mineCount = config.mines;\n\n            // 检查是否需要调整排行榜显示\n            updateLayoutForDifficulty(difficulty);\n\n            // 立即清空游戏板\n            const boardGrid = document.getElementById('board-grid');\n            boardGrid.innerHTML = '';\n            boardGrid.removeAttribute('style');\n            boardGrid.className = '';\n\n            // 强制重新计算CSS变量\n            game.calculateOptimalCellSize();\n\n            // 多次强制刷新确保CSS变量生效\n            for (let i = 0; i < 5; i++) {\n                document.body.offsetHeight;\n                document.documentElement.offsetHeight;\n            }\n\n            // 使用多重异步确保完全更新\n            setTimeout(() => {\n                requestAnimationFrame(() => {\n                    setTimeout(() => {\n                        // 完整重新初始化游戏\n                        game.initGame();\n                    }, 10);\n                });\n            }, 10);\n        }\n\n        // 根据难度调整布局\n        function updateLayoutForDifficulty(difficulty) {\n            const leaderboardPanel = document.querySelector('.leaderboard-panel');\n            const leaderboardToggle = document.querySelector('.leaderboard-toggle');\n            const viewportWidth = window.innerWidth;\n\n            // 确保排行榜在大屏幕上显示\n            if (viewportWidth >= 1000) {\n                if (leaderboardPanel) {\n                    leaderboardPanel.style.display = 'block';\n                }\n                if (leaderboardToggle) {\n                    leaderboardToggle.style.display = 'none';\n                }\n            } else {\n                // 小屏幕时显示切换按钮\n                if (leaderboardToggle) {\n                    leaderboardToggle.style.display = 'inline-block';\n                }\n            }\n\n            // 使用防抖机制重新计算布局，避免频繁调用\n            if (game && game.calculateOptimalCellSize) {\n                debouncedResize();\n            }\n        }\n        \n        function newGame() {\n            game.initGame();\n        }\n        \n        function showHelp() {\n            const modal = document.getElementById('help-modal');\n            modal.style.display = 'flex';\n            setTimeout(() => {\n                modal.classList.add('show');\n            }, 10);\n        }\n\n        // 关闭提示框\n        function closeModal() {\n            const modal = document.getElementById('game-modal');\n            modal.classList.remove('show');\n            setTimeout(() => {\n                modal.style.display = 'none';\n            }, 300);\n        }\n\n        // 从提示框开始新游戏\n        function newGameFromModal() {\n            closeModal();\n            newGame();\n        }\n\n        // 关闭帮助提示框\n        function closeHelpModal() {\n            const modal = document.getElementById('help-modal');\n            modal.classList.remove('show');\n            setTimeout(() => {\n                modal.style.display = 'none';\n            }, 300);\n        }\n\n        // 排行榜相关函数\n        function switchLeaderboardDifficulty(difficulty) {\n            if (window.leaderboardManager) {\n                window.leaderboardManager.switchDifficulty(difficulty);\n            }\n        }\n\n        function toggleLeaderboard() {\n            const leaderboardPanel = document.querySelector('.leaderboard-panel');\n            const toggleBtn = document.getElementById('leaderboard-toggle');\n\n            if (leaderboardPanel) {\n                if (leaderboardPanel.style.display === 'none') {\n                    leaderboardPanel.style.display = 'block';\n                    toggleBtn.textContent = '❌ 隐藏排行榜';\n                    // 加载排行榜数据\n                    if (window.leaderboardManager) {\n                        window.leaderboardManager.loadLeaderboard('beginner');\n                    }\n                } else {\n                    leaderboardPanel.style.display = 'none';\n                    toggleBtn.textContent = '📊 排行榜';\n                }\n                // 使用防抖机制重新计算布局\n                debouncedResize();\n            }\n        }\n\n        // 成绩上传相关函数\n        function showUploadDialog() {\n            if (!window.currentScore) return;\n\n            const modal = document.getElementById('upload-modal');\n            const uploadInfo = document.getElementById('upload-info');\n            const usernameInput = document.getElementById('username-input');\n\n            // 更新上传信息显示\n            const difficultyNames = {\n                'beginner': '初级',\n                'intermediate': '中级',\n                'expert': '专家'\n            };\n\n            uploadInfo.innerHTML = '用时：' + window.currentScore.time + '秒<br>难度：' + difficultyNames[window.currentScore.difficulty];\n\n            // 清空输入框\n            usernameInput.value = '';\n            usernameInput.focus();\n\n            // 显示对话框\n            modal.style.display = 'flex';\n            setTimeout(() => {\n                modal.classList.add('show');\n            }, 10);\n        }\n\n        function closeUploadModal() {\n            const modal = document.getElementById('upload-modal');\n            modal.classList.remove('show');\n            setTimeout(() => {\n                modal.style.display = 'none';\n            }, 300);\n        }\n\n        async function submitScore() {\n            const usernameInput = document.getElementById('username-input');\n            const submitBtn = document.querySelector('#upload-modal .modal-button.primary');\n            const submitText = document.getElementById('submit-text');\n            const submitSpinner = document.getElementById('submit-spinner');\n\n            const username = usernameInput.value.trim();\n\n            // 验证用户名\n            if (!username) {\n                alert('请输入用户名');\n                usernameInput.focus();\n                return;\n            }\n\n            if (username.length > 20) {\n                alert('用户名不能超过20个字符');\n                usernameInput.focus();\n                return;\n            }\n\n            if (!/^[a-zA-Z0-9\\u4e00-\\u9fa5_-]+$/.test(username)) {\n                alert('用户名只能包含字母、数字、中文、下划线和连字符');\n                usernameInput.focus();\n                return;\n            }\n\n            // 显示加载状态\n            submitBtn.disabled = true;\n            submitText.style.display = 'none';\n            submitSpinner.style.display = 'inline-block';\n\n            try {\n                const result = await window.leaderboardManager.uploadScore(\n                    username,\n                    window.currentScore.time,\n                    window.currentScore.difficulty\n                );\n\n                if (result.success) {\n                    // 上传成功\n                    closeUploadModal();\n                    closeModal();\n                    alert('🎉 成绩上传成功！\\n快去排行榜看看你的排名吧！');\n                } else {\n                    // 上传失败\n                    alert('上传失败：' + (result.error || '未知错误'));\n                }\n            } catch (error) {\n                console.error('提交成绩错误:', error);\n                alert('上传失败：网络连接错误');\n            } finally {\n                // 恢复按钮状态\n                submitBtn.disabled = false;\n                submitText.style.display = 'inline';\n                submitSpinner.style.display = 'none';\n            }\n        }\n\n        // 页面加载完成后初始化\n        document.addEventListener('DOMContentLoaded', () => {\n            // 初始化排行榜\n            if (window.leaderboardManager) {\n                window.leaderboardManager.loadLeaderboard('beginner');\n            }\n\n            // 确保游戏正确初始化\n            if (game) {\n                game.initGame();\n            }\n\n            // 添加窗口大小变化事件监听器\n            window.addEventListener('resize', () => {\n                // 使用节流函数提供即时反馈\n                throttledResize();\n                // 使用防抖函数进行最终的精确计算\n                debouncedResize();\n            });\n\n            // 添加方向变化事件监听器（移动设备）\n            window.addEventListener('orientationchange', () => {\n                // 方向变化后稍微延迟，等待浏览器完成布局调整\n                setTimeout(() => {\n                    debouncedResize();\n                }, 300);\n            });\n\n            console.log('窗口大小变化监听器已初始化');\n        });\n\n        // 支持回车键提交成绩\n        document.addEventListener('keydown', (e) => {\n            if (e.key === 'Enter') {\n                const uploadModal = document.getElementById('upload-modal');\n                if (uploadModal && uploadModal.style.display === 'flex') {\n                    submitScore();\n                }\n            }\n        });\n    </script>\n</body>\n</html>`;\n}\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"E:\\\\Work\\\\mine\\\\src\\\\index.js\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"E:\\\\Work\\\\mine\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"E:\\\\Work\\\\mine\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"E:\\\\Work\\\\mine\\\\src\\\\index.js\";\n\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"E:\\\\Work\\\\mine\\\\.wrangler\\\\tmp\\\\bundle-hQIGZL\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"E:\\\\Work\\\\mine\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"E:\\\\Work\\\\mine\\\\.wrangler\\\\tmp\\\\bundle-hQIGZL\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"E:\\\\Work\\\\mine\\\\.wrangler\\\\tmp\\\\bundle-hQIGZL\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS;AAAA;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;AC7BD,SAAS,0BAA0B,OAAO,MAAM;AAC/C,QAAM,UAAU,IAAI,QAAQ,OAAO,IAAI;AACvC,UAAQ,QAAQ,OAAO,kBAAkB;AACzC,SAAO;AACR;AAJS;AAMT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,WAAO,QAAQ,MAAM,QAAQ,SAAS;AAAA,MACrC,0BAA0B,MAAM,MAAM,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACF;AACD,CAAC;;;ACRD,eAAe,qBAAqB,SAAS,KAAK,KAAK;AACrD,QAAM,YAAY,IAAI,SAAS,MAAM,GAAG;AACxC,QAAM,aAAa,UAAU,CAAC;AAG9B,MAAI,CAAC,CAAC,YAAY,gBAAgB,QAAQ,EAAE,SAAS,UAAU,GAAG;AAChE,WAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,OAAO,OAAO,6CAAU,CAAC,GAAG;AAAA,MACxE,QAAQ;AAAA,MACR,SAAS,EAAE,gBAAgB,oBAAoB,+BAA+B,IAAI;AAAA,IACpF,CAAC;AAAA,EACH;AAEA,MAAI;AACF,QAAI,QAAQ,WAAW,OAAO;AAE5B,YAAM,cAAc,MAAM,eAAe,KAAK,UAAU;AACxD,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,MAAM,MAAM,YAAY,CAAC,GAAG;AAAA,QACxE,SAAS,EAAE,gBAAgB,oBAAoB,+BAA+B,IAAI;AAAA,MACpF,CAAC;AAAA,IACH,WAAW,QAAQ,WAAW,QAAQ;AAEpC,YAAM,OAAO,MAAM,QAAQ,KAAK;AAChC,YAAM,EAAE,UAAU,KAAK,IAAI;AAG3B,YAAM,aAAa,cAAc,UAAU,MAAM,UAAU;AAC3D,UAAI,CAAC,WAAW,OAAO;AACrB,eAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,OAAO,OAAO,WAAW,MAAM,CAAC,GAAG;AAAA,UAC/E,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,oBAAoB,+BAA+B,IAAI;AAAA,QACpF,CAAC;AAAA,MACH;AAGA,YAAM,qBAAqB,MAAM,kBAAkB,KAAK,YAAY,UAAU,IAAI;AAClF,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,MAAM,MAAM,mBAAmB,CAAC,GAAG;AAAA,QAC/E,SAAS,EAAE,gBAAgB,oBAAoB,+BAA+B,IAAI;AAAA,MACpF,CAAC;AAAA,IACH,WAAW,QAAQ,WAAW,WAAW;AAEvC,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS;AAAA,UACP,+BAA+B;AAAA,UAC/B,gCAAgC;AAAA,UAChC,gCAAgC;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,SAAS,OAAP;AACA,YAAQ,MAAM,sCAAa,KAAK;AAChC,WAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,OAAO,OAAO,6CAAU,CAAC,GAAG;AAAA,MACxE,QAAQ;AAAA,MACR,SAAS,EAAE,gBAAgB,oBAAoB,+BAA+B,IAAI;AAAA,IACpF,CAAC;AAAA,EACH;AAEA,SAAO,IAAI,SAAS,sBAAsB,EAAE,QAAQ,IAAI,CAAC;AAC3D;AAzDe;AA4Df,eAAe,eAAe,KAAK,YAAY;AAC7C,MAAI;AACF,UAAM,OAAO,MAAM,IAAI,YAAY,IAAI,iBAAiB,UAAU;AAClE,WAAO,OAAO,KAAK,MAAM,IAAI,IAAI,CAAC;AAAA,EACpC,SAAS,OAAP;AACA,YAAQ,MAAM,+CAAY,KAAK;AAC/B,WAAO,CAAC;AAAA,EACV;AACF;AARe;AAWf,eAAe,kBAAkB,KAAK,YAAY,UAAU,MAAM;AAChE,MAAI;AACF,UAAM,cAAc,MAAM,eAAe,KAAK,UAAU;AAGxD,UAAM,WAAW,YAAY,OAAO,YAAU,OAAO,aAAa,QAAQ;AAG1E,aAAS,KAAK;AAAA,MACZ,UAAU,SAAS,KAAK,EAAE,UAAU,GAAG,EAAE;AAAA;AAAA,MACzC,MAAM,SAAS,IAAI;AAAA,MACnB,WAAW,KAAK,IAAI;AAAA,IACtB,CAAC;AAGD,UAAM,SAAS,SAAS,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,GAAG,EAAE;AAEnE,UAAM,IAAI,YAAY,IAAI,iBAAiB,YAAY,KAAK,UAAU,MAAM,CAAC;AAC7E,WAAO;AAAA,EACT,SAAS,OAAP;AACA,YAAQ,MAAM,+CAAY,KAAK;AAC/B,UAAM;AAAA,EACR;AACF;AAvBe;AA0Bf,SAAS,cAAc,UAAU,MAAM,YAAY;AAEjD,MAAI,CAAC,YAAY,SAAS,WAAW,KAAK,SAAS,SAAS,IAAI;AAC9D,WAAO,EAAE,OAAO,OAAO,OAAO,+EAAmB;AAAA,EACnD;AAEA,MAAI,CAAC,gCAAgC,KAAK,QAAQ,GAAG;AACnD,WAAO,EAAE,OAAO,OAAO,OAAO,6IAA0B;AAAA,EAC1D;AAGA,QAAM,WAAW,EAAE,UAAU,GAAG,cAAc,IAAI,QAAQ,GAAG;AAC7D,QAAM,WAAW,EAAE,UAAU,KAAK,cAAc,MAAM,QAAQ,KAAK;AAEnE,MAAI,CAAC,OAAO,UAAU,IAAI,KAAK,OAAO,SAAS,UAAU,KAAK,OAAO,SAAS,UAAU,GAAG;AACzF,WAAO,EAAE,OAAO,OAAO,OAAO,2EAAe;AAAA,EAC/C;AAEA,SAAO,EAAE,OAAO,KAAK;AACvB;AAnBS;AAqBT,IAAO,cAAQ;AAAA,EACb,MAAM,MAAM,SAAS,KAAK,KAAK;AAC7B,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAG/B,QAAI,IAAI,SAAS,WAAW,mBAAmB,GAAG;AAChD,aAAO,qBAAqB,SAAS,KAAK,GAAG;AAAA,IAC/C;AAEA,QAAI,IAAI,aAAa,KAAK;AACxB,aAAO,IAAI,SAAS,YAAY,GAAG;AAAA,QACjC,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC;AAAA,EAClD;AACF;AAEA,SAAS,cAAc;AACrB,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAy8ET;AA18ES;;;AC9IT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAP;AACD,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAP;AACD,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAoE;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EARS;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,iCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAlBM;AAoBN,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,CACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,IAEA,cAA0B,CAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD;AAAA,IAEA,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": []}