{"version": 3, "sources": ["../bundle-JYa50Q/checked-fetch.js", "../bundle-JYa50Q/strip-cf-connecting-ip-header.js", "../../../src/simple.js", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-JYa50Q/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-JYa50Q/middleware-loader.entry.ts"], "sourceRoot": "E:\\Work\\mine\\.wrangler\\tmp\\dev-vtzXY6", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "function stripCfConnectingIPHeader(input, init) {\n\tconst request = new Request(input, init);\n\trequest.headers.delete(\"CF-Connecting-IP\");\n\treturn request;\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\treturn Reflect.apply(target, thisArg, [\n\t\t\tstripCfConnectingIPHeader.apply(null, argArray),\n\t\t]);\n\t},\n});\n", "// 排行榜API处理\nasync function handleLeaderboardAPI(request, env, url) {\n  const difficulty = url.pathname.split('/').pop();\n  \n  if (request.method === 'GET') {\n    try {\n      const data = await env.LEADERBOARD.get('leaderboard:' + difficulty);\n      const leaderboard = data ? JSON.parse(data) : [];\n      return new Response(JSON.stringify({ success: true, data: leaderboard }), {\n        headers: { 'Content-Type': 'application/json' },\n      });\n    } catch (error) {\n      return new Response(JSON.stringify({ success: false, error: error.message }), {\n        status: 500,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n  }\n  \n  if (request.method === 'POST') {\n    try {\n      const { username, time } = await request.json();\n      \n      if (!username || !time) {\n        return new Response(JSON.stringify({ success: false, error: '缺少必要参数' }), {\n          status: 400,\n          headers: { 'Content-Type': 'application/json' },\n        });\n      }\n      \n      const data = await env.LEADERBOARD.get('leaderboard:' + difficulty);\n      const leaderboard = data ? JSON.parse(data) : [];\n      \n      leaderboard.push({ username, time, date: new Date().toISOString() });\n      leaderboard.sort((a, b) => a.time - b.time);\n      const top10 = leaderboard.slice(0, 10);\n      \n      await env.LEADERBOARD.put('leaderboard:' + difficulty, JSON.stringify(top10));\n      \n      return new Response(JSON.stringify({ success: true, data: top10 }), {\n        headers: { 'Content-Type': 'application/json' },\n      });\n    } catch (error) {\n      return new Response(JSON.stringify({ success: false, error: error.message }), {\n        status: 500,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n  }\n  \n  return new Response('Method not allowed', { status: 405 });\n}\n\nexport default {\n  async fetch(request, env, ctx) {\n    const url = new URL(request.url);\n\n    if (url.pathname.startsWith('/api/leaderboard/')) {\n      return handleLeaderboardAPI(request, env, url);\n    }\n\n    if (url.pathname === '/') {\n      return new Response(getGameHTML(), {\n        headers: {\n          'Content-Type': 'text/html;charset=UTF-8',\n          'Cache-Control': 'public, max-age=3600',\n        },\n      });\n    }\n\n    return new Response('Not Found', { status: 404 });\n  },\n};\n\nfunction getGameHTML() {\n  return `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>经典扫雷 - Classic Minesweeper</title>\n    <style>\n        :root {\n            --cell-size: 30px;\n            --counter-font-size: 24px;\n            --smiley-size: 40px;\n        }\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n        body {\n            font-family: 'MS Sans Serif', sans-serif;\n            background-color: #c0c0c0;\n            display: flex;\n            justify-content: center;\n            align-items: flex-start;\n            min-height: 100vh;\n            user-select: none;\n            padding: 20px 10px;\n        }\n        .main-container {\n            display: flex;\n            gap: 20px;\n            width: 100%;\n            max-width: 1600px;\n            align-items: flex-start;\n            justify-content: center;\n        }\n        .game-container {\n            flex: 1;\n            min-width: 400px;\n            background-color: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            padding: 10px;\n            display: flex;\n            flex-direction: column;\n            align-items: center;\n        }\n        .difficulty-selector {\n            margin-bottom: 10px;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            width: 100%;\n        }\n        .difficulty-buttons {\n            display: flex;\n            gap: 5px;\n        }\n        .difficulty-button {\n            background-color: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            padding: 6px 12px;\n            cursor: pointer;\n            font-size: 12px;\n            font-weight: bold;\n            min-width: 60px;\n        }\n        .difficulty-button:active {\n            border: 2px inset #c0c0c0;\n        }\n        .difficulty-button.active {\n            border: 2px inset #c0c0c0;\n            background-color: #a0a0a0;\n        }\n        .help-button {\n            background-color: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            padding: 6px 12px;\n            cursor: pointer;\n            font-size: 12px;\n            font-weight: bold;\n        }\n        .help-button:active {\n            border: 2px inset #c0c0c0;\n        }\n        .game-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            background-color: #c0c0c0;\n            border: 2px inset #c0c0c0;\n            padding: 3px 6px;\n            margin-bottom: 10px;\n            width: 100%;\n        }\n        .counter {\n            background-color: #000;\n            color: #ff0000;\n            font-family: 'Courier New', monospace;\n            font-size: var(--counter-font-size);\n            font-weight: bold;\n            padding: 3px 6px;\n            border: 1px inset #808080;\n            min-width: calc(var(--counter-font-size) * 2.5);\n            text-align: center;\n        }\n        .smiley-button {\n            width: var(--smiley-size);\n            height: var(--smiley-size);\n            font-size: calc(var(--smiley-size) * 0.6);\n            background-color: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }\n        .smiley-button:active {\n            border: 2px inset #c0c0c0;\n        }\n        .game-board {\n            border: 2px inset #c0c0c0;\n            display: flex;\n            justify-content: center;\n            align-items: center;\n            background-color: #c0c0c0;\n            padding: 2px;\n        }\n        .board-grid {\n            display: grid;\n            gap: 0;\n            border: 1px solid #808080;\n        }\n        .cell {\n            width: var(--cell-size);\n            height: var(--cell-size);\n            border: 1px outset #c0c0c0;\n            background-color: #c0c0c0;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-size: calc(var(--cell-size) * 0.6);\n            font-weight: bold;\n            cursor: pointer;\n        }\n        .cell:active {\n            border: 1px inset #c0c0c0;\n        }\n        .cell.revealed {\n            border: 1px solid #808080;\n            background-color: #ffffff;\n        }\n        .cell.mine {\n            background-color: #ff0000;\n        }\n        .cell.flagged::after {\n            content: '🚩';\n            font-size: calc(var(--cell-size) * 0.7);\n        }\n        .cell.number-1 { color: #0000ff; }\n        .cell.number-2 { color: #008000; }\n        .cell.number-3 { color: #ff0000; }\n        .cell.number-4 { color: #000080; }\n        .cell.number-5 { color: #800000; }\n        .cell.number-6 { color: #008080; }\n        .cell.number-7 { color: #000000; }\n        .cell.number-8 { color: #808080; }\n        \n        /* 排行榜面板样式 */\n        .leaderboard-panel {\n            width: 280px;\n            background-color: #c0c0c0;\n            border: 2px inset #c0c0c0;\n            padding: 10px;\n            height: fit-content;\n            max-height: 80vh;\n            overflow-y: auto;\n        }\n        .leaderboard-header h3 {\n            margin: 0 0 10px 0;\n            font-size: 16px;\n            text-align: center;\n            color: #000080;\n        }\n        .leaderboard-tabs {\n            display: flex;\n            gap: 2px;\n            margin-bottom: 15px;\n        }\n        .tab-button {\n            flex: 1;\n            padding: 6px 4px;\n            font-size: 11px;\n            background: #c0c0c0;\n            border: 1px outset #c0c0c0;\n            cursor: pointer;\n        }\n        .tab-button.active {\n            background: #ffffff;\n            border: 1px inset #c0c0c0;\n        }\n        .leaderboard-list {\n            display: block;\n        }\n        .leaderboard-item {\n            display: flex;\n            align-items: center;\n            padding: 8px 10px;\n            margin: 2px 0;\n            background: #f0f0f0;\n            border: 1px solid #808080;\n            font-size: 12px;\n        }\n        .leaderboard-rank {\n            font-weight: bold;\n            color: #666;\n            min-width: 25px;\n            text-align: center;\n        }\n        .leaderboard-username {\n            flex: 1;\n            margin: 0 8px;\n            font-weight: bold;\n        }\n        .leaderboard-time {\n            font-family: 'Courier New', monospace;\n            font-weight: bold;\n            color: #d00;\n            font-size: 11px;\n        }\n        \n        /* 美化的模态框样式 */\n        .modal {\n            display: none;\n            position: fixed;\n            z-index: 1000;\n            left: 0;\n            top: 0;\n            width: 100%;\n            height: 100%;\n            background-color: rgba(0, 0, 0, 0.5);\n            animation: fadeIn 0.3s ease-out;\n        }\n\n        .modal-content {\n            background-color: #c0c0c0;\n            margin: 15% auto;\n            padding: 0;\n            border: 3px outset #c0c0c0;\n            width: 90%;\n            max-width: 400px;\n            border-radius: 0;\n            animation: slideIn 0.3s ease-out;\n        }\n\n        .modal-header {\n            background: linear-gradient(90deg, #000080, #0000ff);\n            color: white;\n            padding: 8px 12px;\n            font-weight: bold;\n            font-size: 14px;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n\n        .modal-close {\n            background: #c0c0c0;\n            border: 1px outset #c0c0c0;\n            width: 20px;\n            height: 18px;\n            cursor: pointer;\n            font-size: 12px;\n            font-weight: bold;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }\n\n        .modal-close:active {\n            border: 1px inset #c0c0c0;\n        }\n\n        .modal-body {\n            padding: 20px;\n            text-align: center;\n            font-size: 14px;\n            line-height: 1.5;\n        }\n\n        .modal-icon {\n            font-size: 48px;\n            margin-bottom: 15px;\n            display: block;\n        }\n\n        .modal-buttons {\n            margin-top: 20px;\n            display: flex;\n            gap: 10px;\n            justify-content: center;\n        }\n\n        .modal-button {\n            background: #c0c0c0;\n            border: 2px outset #c0c0c0;\n            padding: 8px 16px;\n            cursor: pointer;\n            font-size: 12px;\n            font-weight: bold;\n            min-width: 80px;\n        }\n\n        .modal-button:active {\n            border: 2px inset #c0c0c0;\n        }\n\n        .modal-button.primary {\n            background: #0078d4;\n            color: white;\n            border: 2px outset #0078d4;\n        }\n\n        .modal-button.primary:active {\n            border: 2px inset #0078d4;\n        }\n\n        .modal-input {\n            width: 100%;\n            padding: 6px;\n            border: 2px inset #c0c0c0;\n            font-size: 14px;\n            margin: 10px 0;\n        }\n\n        @keyframes fadeIn {\n            from { opacity: 0; }\n            to { opacity: 1; }\n        }\n\n        @keyframes slideIn {\n            from { transform: translateY(-50px); opacity: 0; }\n            to { transform: translateY(0); opacity: 1; }\n        }\n\n        @media (max-width: 768px) {\n            .leaderboard-panel {\n                display: none;\n            }\n            .modal-content {\n                width: 95%;\n                margin: 20% auto;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"main-container\">\n        <!-- 排行榜面板 -->\n        <div class=\"leaderboard-panel\">\n            <div class=\"leaderboard-header\">\n                <h3>🏆 排行榜</h3>\n                <div class=\"leaderboard-tabs\">\n                    <button class=\"tab-button active\" onclick=\"switchLeaderboard('beginner')\">初级</button>\n                    <button class=\"tab-button\" onclick=\"switchLeaderboard('intermediate')\">中级</button>\n                    <button class=\"tab-button\" onclick=\"switchLeaderboard('expert')\">专家</button>\n                </div>\n            </div>\n            <div class=\"leaderboard-list\" id=\"leaderboard-list\">\n                <div style=\"text-align: center; padding: 20px; color: #666;\">加载中...</div>\n            </div>\n        </div>\n\n        <!-- 游戏区域 -->\n        <div class=\"game-container\">\n            <div class=\"difficulty-selector\">\n                <div class=\"difficulty-buttons\">\n                    <button class=\"difficulty-button active\" onclick=\"setDifficulty('beginner')\">初级</button>\n                    <button class=\"difficulty-button\" onclick=\"setDifficulty('intermediate')\">中级</button>\n                    <button class=\"difficulty-button\" onclick=\"setDifficulty('expert')\">专家</button>\n                </div>\n                <button class=\"help-button\" onclick=\"showHelp()\">帮助</button>\n            </div>\n\n            <div class=\"game-header\">\n                <div class=\"counter\" id=\"mine-counter\">010</div>\n                <button class=\"smiley-button\" id=\"smiley-button\" onclick=\"newGame()\">😊</button>\n                <div class=\"counter\" id=\"timer\">000</div>\n            </div>\n\n            <div class=\"game-board\">\n                <div class=\"board-grid\" id=\"board-grid\"></div>\n            </div>\n        </div>\n    </div>\n\n    <!-- 美化的模态框 -->\n    <div id=\"game-modal\" class=\"modal\">\n        <div class=\"modal-content\">\n            <div class=\"modal-header\">\n                <span id=\"modal-title\">游戏提示</span>\n                <button class=\"modal-close\" onclick=\"closeModal()\">×</button>\n            </div>\n            <div class=\"modal-body\">\n                <span id=\"modal-icon\" class=\"modal-icon\">😊</span>\n                <div id=\"modal-message\">消息内容</div>\n                <div id=\"modal-input-container\" style=\"display: none;\">\n                    <input type=\"text\" id=\"modal-input\" class=\"modal-input\" placeholder=\"请输入您的用户名\" maxlength=\"20\">\n                </div>\n                <div class=\"modal-buttons\">\n                    <button id=\"modal-cancel\" class=\"modal-button\" onclick=\"closeModal()\" style=\"display: none;\">取消</button>\n                    <button id=\"modal-confirm\" class=\"modal-button primary\" onclick=\"handleModalConfirm()\">确定</button>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <script>\n        // 简化的扫雷游戏\n        class MinesweeperGame {\n            constructor() {\n                this.difficulties = {\n                    beginner: { rows: 9, cols: 9, mines: 10 },\n                    intermediate: { rows: 16, cols: 16, mines: 40 },\n                    expert: { rows: 16, cols: 30, mines: 99 }\n                };\n                this.currentDifficulty = 'beginner';\n                this.board = [];\n                this.revealed = [];\n                this.flagged = [];\n                this.gameState = 'ready';\n                this.firstClick = true;\n                this.startTime = null;\n                this.timer = null;\n                this.mineCount = 0;\n                this.flagCount = 0;\n            }\n\n            initGame() {\n                const config = this.difficulties[this.currentDifficulty];\n                this.rows = config.rows;\n                this.cols = config.cols;\n                this.mineCount = config.mines;\n                this.flagCount = 0;\n\n                this.board = Array(this.rows).fill().map(() => Array(this.cols).fill(0));\n                this.revealed = Array(this.rows).fill().map(() => Array(this.cols).fill(false));\n                this.flagged = Array(this.rows).fill().map(() => Array(this.cols).fill(false));\n\n                this.gameState = 'ready';\n                this.firstClick = true;\n                this.startTime = null;\n\n                if (this.timer) {\n                    clearInterval(this.timer);\n                    this.timer = null;\n                }\n\n                this.createBoard();\n                this.updateDisplay();\n\n                document.getElementById('smiley-button').textContent = '😊';\n                document.getElementById('timer').textContent = '000';\n            }\n\n            createBoard() {\n                const boardGrid = document.getElementById('board-grid');\n                boardGrid.innerHTML = '';\n\n                // 动态计算格子大小\n                this.calculateOptimalCellSize();\n\n                boardGrid.style.gridTemplateColumns = 'repeat(' + this.cols + ', var(--cell-size))';\n                boardGrid.style.gridTemplateRows = 'repeat(' + this.rows + ', var(--cell-size))';\n\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        const cell = document.createElement('div');\n                        cell.className = 'cell';\n                        cell.addEventListener('click', (e) => this.handleLeftClick(row, col, e));\n                        cell.addEventListener('contextmenu', (e) => this.handleRightClick(row, col, e));\n                        boardGrid.appendChild(cell);\n                    }\n                }\n            }\n\n            // 计算最佳格子大小，确保所有内容都在视口内\n            calculateOptimalCellSize() {\n                const viewportWidth = window.innerWidth;\n                const viewportHeight = window.innerHeight;\n\n                // 精确测量实际占用的空间\n                const leaderboardPanel = document.querySelector('.leaderboard-panel');\n                let leaderboardWidth = 0;\n                if (leaderboardPanel && window.getComputedStyle(leaderboardPanel).display !== 'none') {\n                    leaderboardWidth = leaderboardPanel.offsetWidth + 20; // 包括间距\n                }\n\n                // 精确测量顶部控件高度\n                const difficultySelector = document.querySelector('.difficulty-selector');\n                const gameHeader = document.querySelector('.game-header');\n                let topHeight = 0;\n                if (difficultySelector && gameHeader) {\n                    topHeight = difficultySelector.offsetHeight + gameHeader.offsetHeight + 30; // 包括边距\n                }\n\n                // 计算游戏板的最大可用空间（留出安全边距）\n                const safeMargin = 20;\n                const maxBoardWidth = viewportWidth - leaderboardWidth - safeMargin * 2;\n                const maxBoardHeight = viewportHeight - topHeight - safeMargin * 2;\n\n                // 确保最小可用空间\n                const minBoardWidth = Math.max(maxBoardWidth, 200);\n                const minBoardHeight = Math.max(maxBoardHeight, 150);\n\n                // 计算格子大小，确保游戏板完全适应可用空间\n                const maxCellSizeByWidth = Math.floor(minBoardWidth / this.cols);\n                const maxCellSizeByHeight = Math.floor(minBoardHeight / this.rows);\n\n                // 取较小值确保完整显示\n                let optimalSize = Math.min(maxCellSizeByWidth, maxCellSizeByHeight);\n\n                // 设置合理的大小范围，但优先保证完整显示\n                optimalSize = Math.max(12, Math.min(45, optimalSize));\n\n                // 最终验证：确保计算出的游戏板尺寸不会超出视口\n                const finalBoardWidth = optimalSize * this.cols;\n                const finalBoardHeight = optimalSize * this.rows;\n                const totalWidth = finalBoardWidth + leaderboardWidth + safeMargin * 2;\n                const totalHeight = finalBoardHeight + topHeight + safeMargin * 2;\n\n                // 如果仍然超出，进一步缩小\n                if (totalWidth > viewportWidth || totalHeight > viewportHeight) {\n                    const scaleX = viewportWidth / totalWidth;\n                    const scaleY = viewportHeight / totalHeight;\n                    const scale = Math.min(scaleX, scaleY) * 0.95; // 留5%安全边距\n                    optimalSize = Math.floor(optimalSize * scale);\n                    optimalSize = Math.max(10, optimalSize); // 绝对最小值\n                }\n\n                // 更新CSS变量\n                document.documentElement.style.setProperty('--cell-size', optimalSize + 'px');\n                document.documentElement.style.setProperty('--counter-font-size', Math.max(12, optimalSize * 0.7) + 'px');\n                document.documentElement.style.setProperty('--smiley-size', Math.max(24, optimalSize * 1.0) + 'px');\n\n                console.log('布局计算结果:', {\n                    视口: viewportWidth + 'x' + viewportHeight,\n                    排行榜宽度: leaderboardWidth,\n                    顶部高度: topHeight,\n                    可用空间: minBoardWidth + 'x' + minBoardHeight,\n                    游戏尺寸: this.cols + 'x' + this.rows,\n                    格子大小: optimalSize,\n                    最终游戏板: finalBoardWidth + 'x' + finalBoardHeight\n                });\n            }\n\n            generateMines(firstClickRow, firstClickCol) {\n                const positions = [];\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        if (Math.abs(row - firstClickRow) <= 1 && Math.abs(col - firstClickCol) <= 1) {\n                            continue;\n                        }\n                        positions.push([row, col]);\n                    }\n                }\n\n                for (let i = positions.length - 1; i > 0; i--) {\n                    const j = Math.floor(Math.random() * (i + 1));\n                    [positions[i], positions[j]] = [positions[j], positions[i]];\n                }\n\n                for (let i = 0; i < this.mineCount && i < positions.length; i++) {\n                    const [row, col] = positions[i];\n                    this.board[row][col] = -1;\n                }\n\n                this.calculateNumbers();\n            }\n\n            calculateNumbers() {\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        if (this.board[row][col] !== -1) {\n                            let count = 0;\n                            for (let dr = -1; dr <= 1; dr++) {\n                                for (let dc = -1; dc <= 1; dc++) {\n                                    const newRow = row + dr;\n                                    const newCol = col + dc;\n                                    if (this.isValidCell(newRow, newCol) && this.board[newRow][newCol] === -1) {\n                                        count++;\n                                    }\n                                }\n                            }\n                            this.board[row][col] = count;\n                        }\n                    }\n                }\n            }\n\n            isValidCell(row, col) {\n                return row >= 0 && row < this.rows && col >= 0 && col < this.cols;\n            }\n\n            handleLeftClick(row, col, event) {\n                event.preventDefault();\n                if (this.gameState === 'won' || this.gameState === 'lost') return;\n                if (this.flagged[row][col]) return;\n\n                if (this.firstClick) {\n                    this.generateMines(row, col);\n                    this.firstClick = false;\n                    this.gameState = 'playing';\n                    this.startTimer();\n                }\n\n                this.revealCell(row, col);\n                this.updateDisplay();\n                this.checkGameState();\n            }\n\n            handleRightClick(row, col, event) {\n                event.preventDefault();\n                if (this.gameState === 'won' || this.gameState === 'lost') return;\n                if (this.revealed[row][col]) return;\n\n                this.flagged[row][col] = !this.flagged[row][col];\n                this.flagCount += this.flagged[row][col] ? 1 : -1;\n                this.updateDisplay();\n            }\n\n            revealCell(row, col) {\n                if (!this.isValidCell(row, col) || this.revealed[row][col] || this.flagged[row][col]) {\n                    return;\n                }\n\n                this.revealed[row][col] = true;\n\n                if (this.board[row][col] === -1) {\n                    this.gameState = 'lost';\n                    this.revealAllMines();\n                    return;\n                }\n\n                if (this.board[row][col] === 0) {\n                    for (let dr = -1; dr <= 1; dr++) {\n                        for (let dc = -1; dc <= 1; dc++) {\n                            this.revealCell(row + dr, col + dc);\n                        }\n                    }\n                }\n            }\n\n            revealAllMines() {\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        if (this.board[row][col] === -1) {\n                            this.revealed[row][col] = true;\n                        }\n                    }\n                }\n            }\n\n            checkGameState() {\n                if (this.gameState === 'lost') {\n                    document.getElementById('smiley-button').textContent = '😵';\n                    this.stopTimer();\n                    setTimeout(() => {\n                        showModal('游戏失败', '💣', '踩到地雷了！<br>点击笑脸或新游戏按钮重新开始。');\n                    }, 100);\n                    return;\n                }\n\n                let unrevealedCount = 0;\n                for (let row = 0; row < this.rows; row++) {\n                    for (let col = 0; col < this.cols; col++) {\n                        if (!this.revealed[row][col] && this.board[row][col] !== -1) {\n                            unrevealedCount++;\n                        }\n                    }\n                }\n\n                if (unrevealedCount === 0) {\n                    this.gameState = 'won';\n                    document.getElementById('smiley-button').textContent = '😎';\n                    this.stopTimer();\n\n                    for (let row = 0; row < this.rows; row++) {\n                        for (let col = 0; col < this.cols; col++) {\n                            if (this.board[row][col] === -1 && !this.flagged[row][col]) {\n                                this.flagged[row][col] = true;\n                                this.flagCount++;\n                            }\n                        }\n                    }\n\n                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);\n                    setTimeout(async () => {\n                        const message = '用时：' + elapsed + '秒<br>难度：' + this.getDifficultyName() + '<br><br>太棒了！你成功找出了所有地雷！<br>请输入您的用户名上传成绩：';\n                        const username = await showModal('恭喜胜利！', '🎉', message, true, true);\n                        if (username && username.trim()) {\n                            uploadScore(username.trim(), elapsed, this.currentDifficulty);\n                        }\n                    }, 100);\n                }\n            }\n\n            startTimer() {\n                this.startTime = Date.now();\n                this.timer = setInterval(() => {\n                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);\n                    const displayTime = Math.min(elapsed, 999);\n                    document.getElementById('timer').textContent = displayTime.toString().padStart(3, '0');\n                }, 1000);\n            }\n\n            stopTimer() {\n                if (this.timer) {\n                    clearInterval(this.timer);\n                    this.timer = null;\n                }\n            }\n\n            getDifficultyName() {\n                const names = {\n                    beginner: '初级',\n                    intermediate: '中级',\n                    expert: '专家'\n                };\n                return names[this.currentDifficulty] || '未知';\n            }\n\n            updateDisplay() {\n                const remainingMines = this.mineCount - this.flagCount;\n                document.getElementById('mine-counter').textContent =\n                    Math.max(-99, Math.min(999, remainingMines)).toString().padStart(3, '0');\n\n                const cells = document.querySelectorAll('.cell');\n                cells.forEach((cell, index) => {\n                    const row = Math.floor(index / this.cols);\n                    const col = index % this.cols;\n\n                    cell.className = 'cell';\n                    cell.textContent = '';\n\n                    if (this.flagged[row][col]) {\n                        cell.classList.add('flagged');\n                    } else if (this.revealed[row][col]) {\n                        cell.classList.add('revealed');\n                        if (this.board[row][col] === -1) {\n                            cell.classList.add('mine');\n                            cell.textContent = '💣';\n                        } else if (this.board[row][col] > 0) {\n                            cell.classList.add('number-' + this.board[row][col]);\n                            cell.textContent = this.board[row][col];\n                        }\n                    }\n                });\n            }\n        }\n\n        // 全局变量\n        let game = null;\n        let currentLeaderboardDifficulty = 'beginner';\n        let modalCallback = null;\n\n        // 模态框函数\n        function showModal(title, icon, message, showInput = false, showCancel = false) {\n            document.getElementById('modal-title').textContent = title;\n            document.getElementById('modal-icon').textContent = icon;\n            document.getElementById('modal-message').innerHTML = message;\n\n            const inputContainer = document.getElementById('modal-input-container');\n            const cancelButton = document.getElementById('modal-cancel');\n            const input = document.getElementById('modal-input');\n\n            if (showInput) {\n                inputContainer.style.display = 'block';\n                input.value = '';\n                setTimeout(() => input.focus(), 100);\n            } else {\n                inputContainer.style.display = 'none';\n            }\n\n            cancelButton.style.display = showCancel ? 'inline-block' : 'none';\n\n            document.getElementById('game-modal').style.display = 'block';\n\n            return new Promise((resolve) => {\n                modalCallback = resolve;\n            });\n        }\n\n        function closeModal() {\n            document.getElementById('game-modal').style.display = 'none';\n            if (modalCallback) {\n                modalCallback(null);\n                modalCallback = null;\n            }\n        }\n\n        function handleModalConfirm() {\n            const input = document.getElementById('modal-input');\n            const value = input.style.display !== 'none' && input.offsetParent !== null ? input.value.trim() : true;\n\n            document.getElementById('game-modal').style.display = 'none';\n            if (modalCallback) {\n                modalCallback(value);\n                modalCallback = null;\n            }\n        }\n\n        // 点击模态框背景关闭\n        document.addEventListener('click', (e) => {\n            if (e.target.id === 'game-modal') {\n                closeModal();\n            }\n        });\n\n        // 全局函数\n        function setDifficulty(difficulty) {\n            if (!game) return;\n\n            document.querySelectorAll('.difficulty-button').forEach(btn => {\n                btn.classList.remove('active');\n            });\n            event.target.classList.add('active');\n\n            game.currentDifficulty = difficulty;\n            game.initGame();\n        }\n\n        function newGame() {\n            if (game) {\n                game.initGame();\n            }\n        }\n\n        function showHelp() {\n            const helpMessage =\n                '<strong>桌面端：</strong><br>' +\n                '左键点击挖掘格子<br>' +\n                '右键点击标记地雷<br><br>' +\n\n                '<strong>移动端：</strong><br>' +\n                '点击挖掘格子<br>' +\n                '长按标记地雷<br><br>' +\n\n                '<strong>难度选择：</strong><br>' +\n                '• 初级：9×9，10个地雷<br>' +\n                '• 中级：16×16，40个地雷<br>' +\n                '• 专家：30×16，99个地雷<br><br>' +\n\n                '<strong>提示：</strong><br>' +\n                '数字表示周围8个格子中地雷的数量';\n            showModal('游戏帮助', '❓', helpMessage);\n        }\n\n        function switchLeaderboard(difficulty) {\n            currentLeaderboardDifficulty = difficulty;\n\n            document.querySelectorAll('.tab-button').forEach(btn => {\n                btn.classList.remove('active');\n            });\n            event.target.classList.add('active');\n\n            loadLeaderboard(difficulty);\n        }\n\n        async function loadLeaderboard(difficulty) {\n            try {\n                const response = await fetch('/api/leaderboard/' + difficulty);\n                const result = await response.json();\n\n                const listElement = document.getElementById('leaderboard-list');\n                if (result.success && result.data.length > 0) {\n                    listElement.innerHTML = result.data.map((record, index) => {\n                        const rank = index + 1;\n                        return '<div class=\"leaderboard-item\">' +\n                               '<div class=\"leaderboard-rank\">' + rank + '</div>' +\n                               '<div class=\"leaderboard-username\">' + record.username + '</div>' +\n                               '<div class=\"leaderboard-time\">' + record.time + 's</div>' +\n                               '</div>';\n                    }).join('');\n                } else {\n                    listElement.innerHTML = '<div style=\"text-align: center; padding: 20px; color: #666;\">暂无记录</div>';\n                }\n            } catch (error) {\n                console.error('加载排行榜失败:', error);\n                document.getElementById('leaderboard-list').innerHTML =\n                    '<div style=\"text-align: center; padding: 20px; color: #d00;\">加载失败</div>';\n            }\n        }\n\n        async function uploadScore(username, time, difficulty) {\n            try {\n                const response = await fetch('/api/leaderboard/' + difficulty, {\n                    method: 'POST',\n                    headers: { 'Content-Type': 'application/json' },\n                    body: JSON.stringify({ username, time })\n                });\n\n                const result = await response.json();\n                if (result.success) {\n                    showModal('上传成功', '🎉', '成绩上传成功！<br>您的成绩已添加到排行榜中。');\n                    if (currentLeaderboardDifficulty === difficulty) {\n                        loadLeaderboard(difficulty);\n                    }\n                } else {\n                    showModal('上传失败', '❌', '上传失败：' + result.error);\n                }\n            } catch (error) {\n                console.error('上传成绩失败:', error);\n                showModal('上传失败', '❌', '上传失败：网络连接错误<br>请检查网络连接后重试。');\n            }\n        }\n\n        // 初始化\n        window.addEventListener('DOMContentLoaded', () => {\n            game = new MinesweeperGame();\n            game.initGame(); // 确保游戏板被创建\n            loadLeaderboard('beginner');\n\n            // 添加窗口大小变化监听器\n            window.addEventListener('resize', () => {\n                if (game) {\n                    game.calculateOptimalCellSize();\n                }\n            });\n        });\n    </script>\n</body>\n</html>`;\n}\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"E:\\\\Work\\\\mine\\\\src\\\\simple.js\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"E:\\\\Work\\\\mine\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"E:\\\\Work\\\\mine\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"E:\\\\Work\\\\mine\\\\src\\\\simple.js\";\n\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"E:\\\\Work\\\\mine\\\\.wrangler\\\\tmp\\\\bundle-JYa50Q\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"E:\\\\Work\\\\mine\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"E:\\\\Work\\\\mine\\\\.wrangler\\\\tmp\\\\bundle-JYa50Q\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"E:\\\\Work\\\\mine\\\\.wrangler\\\\tmp\\\\bundle-JYa50Q\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS;AAAA;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;AC7BD,SAAS,0BAA0B,OAAO,MAAM;AAC/C,QAAM,UAAU,IAAI,QAAQ,OAAO,IAAI;AACvC,UAAQ,QAAQ,OAAO,kBAAkB;AACzC,SAAO;AACR;AAJS;AAMT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,WAAO,QAAQ,MAAM,QAAQ,SAAS;AAAA,MACrC,0BAA0B,MAAM,MAAM,QAAQ;AAAA,IAC/C,CAAC;AAAA,EACF;AACD,CAAC;;;ACXD,eAAe,qBAAqB,SAAS,KAAK,KAAK;AACrD,QAAM,aAAa,IAAI,SAAS,MAAM,GAAG,EAAE,IAAI;AAE/C,MAAI,QAAQ,WAAW,OAAO;AAC5B,QAAI;AACF,YAAM,OAAO,MAAM,IAAI,YAAY,IAAI,iBAAiB,UAAU;AAClE,YAAM,cAAc,OAAO,KAAK,MAAM,IAAI,IAAI,CAAC;AAC/C,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,MAAM,MAAM,YAAY,CAAC,GAAG;AAAA,QACxE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH,SAAS,OAAP;AACA,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,OAAO,OAAO,MAAM,QAAQ,CAAC,GAAG;AAAA,QAC5E,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,QAAQ,WAAW,QAAQ;AAC7B,QAAI;AACF,YAAM,EAAE,UAAU,KAAK,IAAI,MAAM,QAAQ,KAAK;AAE9C,UAAI,CAAC,YAAY,CAAC,MAAM;AACtB,eAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,OAAO,OAAO,uCAAS,CAAC,GAAG;AAAA,UACvE,QAAQ;AAAA,UACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,QAChD,CAAC;AAAA,MACH;AAEA,YAAM,OAAO,MAAM,IAAI,YAAY,IAAI,iBAAiB,UAAU;AAClE,YAAM,cAAc,OAAO,KAAK,MAAM,IAAI,IAAI,CAAC;AAE/C,kBAAY,KAAK,EAAE,UAAU,MAAM,OAAM,oBAAI,KAAK,GAAE,YAAY,EAAE,CAAC;AACnE,kBAAY,KAAK,CAAC,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI;AAC1C,YAAM,QAAQ,YAAY,MAAM,GAAG,EAAE;AAErC,YAAM,IAAI,YAAY,IAAI,iBAAiB,YAAY,KAAK,UAAU,KAAK,CAAC;AAE5E,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,MAAM,MAAM,MAAM,CAAC,GAAG;AAAA,QAClE,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH,SAAS,OAAP;AACA,aAAO,IAAI,SAAS,KAAK,UAAU,EAAE,SAAS,OAAO,OAAO,MAAM,QAAQ,CAAC,GAAG;AAAA,QAC5E,QAAQ;AAAA,QACR,SAAS,EAAE,gBAAgB,mBAAmB;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,IAAI,SAAS,sBAAsB,EAAE,QAAQ,IAAI,CAAC;AAC3D;AAlDe;AAoDf,IAAO,iBAAQ;AAAA,EACb,MAAM,MAAM,SAAS,KAAK,KAAK;AAC7B,UAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;AAE/B,QAAI,IAAI,SAAS,WAAW,mBAAmB,GAAG;AAChD,aAAO,qBAAqB,SAAS,KAAK,GAAG;AAAA,IAC/C;AAEA,QAAI,IAAI,aAAa,KAAK;AACxB,aAAO,IAAI,SAAS,YAAY,GAAG;AAAA,QACjC,SAAS;AAAA,UACP,gBAAgB;AAAA,UAChB,iBAAiB;AAAA,QACnB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,IAAI,SAAS,aAAa,EAAE,QAAQ,IAAI,CAAC;AAAA,EAClD;AACF;AAEA,SAAS,cAAc;AACrB,SAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAm6BT;AAp6BS;;;ACxET,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAP;AACD,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAP;AACD,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAoE;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EARS;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,iCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAlBM;AAoBN,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,CACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B;AAAA,IAEA,cAA0B,CAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD;AAAA,IAEA,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": []}