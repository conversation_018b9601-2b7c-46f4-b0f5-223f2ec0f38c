# 部署指南

## 🎮 在线游戏地址

**立即体验：** https://mines.heartwopen.workers.dev

## 🚀 部署到 Cloudflare Workers

### 前提条件
1. 拥有 Cloudflare 账户
2. 已安装 Node.js 和 npm

### 部署步骤

1. **登录 Cloudflare**
```bash
npx wrangler login
```

2. **部署项目**
```bash
npm run deploy
```

3. **访问你的游戏**
部署成功后，Wrangler 会显示你的游戏 URL，例如：
```
https://mines.heartwopen.workers.dev
```

**当前部署示例：** https://mines.heartwopen.workers.dev

### 自定义域名（可选）

如果你想使用自定义域名，可以在 Cloudflare Dashboard 中配置：

1. 进入 Workers & Pages
2. 选择你的 Worker
3. 点击 "Custom domains"
4. 添加你的域名

### 本地开发

```bash
# 安装依赖
npm install

# 启动本地开发服务器
npm run dev

# 访问 http://localhost:8787
```

### 更新游戏

修改代码后，重新运行部署命令即可：
```bash
npm run deploy
```

---

🎮 享受你的经典扫雷游戏吧！
