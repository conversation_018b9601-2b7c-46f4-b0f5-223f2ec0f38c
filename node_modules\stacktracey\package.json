{"name": "<PERSON><PERSON><PERSON>", "version": "2.1.8", "description": "Parses call stacks. Reads sources. Clean & filtered output. Sourcemaps. Node & browsers.", "main": "<PERSON><PERSON><PERSON>", "types": "./stacktracey.d.ts", "scripts": {"lint": "eslint stack<PERSON>cey.js", "test": "nyc --reporter=html --reporter=text mocha --reporter spec", "test-no-coverage": "mocha --reporter spec", "autotest": "mocha --reporter spec --watch", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "repository": {"type": "git", "url": "https://github.com/xpl/stacktracey.git"}, "keywords": ["stack", "stacktrace", "stack printer", "error printer", "print error", "print stack", "print debug trace", "debug", "trace", "parse", "print", "pretty", "callstack", "call stack", "read sources", "error", "Error.stack", "error stack", "stack parse", "call stack parsing", "stack parsing", "stack parse", "parse stack", "parse call stack", "callstack parser", "call stack parse", "parse callstack", "parse stacktrace", "parse stack trace", "error stack parse", "Error.stack parse", "stack parser", "error parser", "error trace", "trace error", "sourcemap", "call location", "source text", "source lines", "print error with sources", "show error", "handling exceptions", "exception parser", "exception printer", "custom error", "pretty print", "pretty print errors", "pretty print stack"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "Unlicense", "bugs": {"url": "https://github.com/xpl/stacktracey/issues"}, "homepage": "https://xpl.github.io/stacktracey", "devDependencies": {"babel-cli": "^6.26.0", "chai": "^4.2.0", "coveralls": "^3.0.3", "eslint": "^4.19.1", "istanbul": "^0.4.5", "mocha": "^4.1.0", "nyc": "^14.1.1"}, "dependencies": {"as-table": "^1.0.36", "get-source": "^2.0.12"}}