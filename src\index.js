// 经典扫雷游戏 - <PERSON>flare Workers 版本
// 作者: Claude 4.0 sonnet

// 排行榜API处理函数
async function handleLeaderboardAPI(request, env, url) {
  const pathParts = url.pathname.split('/');
  const difficulty = pathParts[3]; // /api/leaderboard/{difficulty}

  // 验证难度参数
  if (!['beginner', 'intermediate', 'expert'].includes(difficulty)) {
    return new Response(JSON.stringify({ success: false, error: '无效的难度级别' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
    });
  }

  try {
    if (request.method === 'GET') {
      // 获取排行榜
      const leaderboard = await getLeaderboard(env, difficulty);
      return new Response(JSON.stringify({ success: true, data: leaderboard }), {
        headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
      });
    } else if (request.method === 'POST') {
      // 上传成绩
      const body = await request.json();
      const { username, time } = body;

      // 验证数据
      const validation = validateScore(username, time, difficulty);
      if (!validation.valid) {
        return new Response(JSON.stringify({ success: false, error: validation.error }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
        });
      }

      // 更新排行榜
      const updatedLeaderboard = await updateLeaderboard(env, difficulty, username, time);
      return new Response(JSON.stringify({ success: true, data: updatedLeaderboard }), {
        headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
      });
    } else if (request.method === 'OPTIONS') {
      // 处理CORS预检请求
      return new Response(null, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type',
        }
      });
    }
  } catch (error) {
    console.error('排行榜API错误:', error);
    return new Response(JSON.stringify({ success: false, error: '服务器内部错误' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', 'Access-Control-Allow-Origin': '*' }
    });
  }

  return new Response('Method Not Allowed', { status: 405 });
}

// 获取排行榜数据
async function getLeaderboard(env, difficulty) {
  try {
    const data = await env.LEADERBOARD.get(`leaderboard:${difficulty}`);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('获取排行榜失败:', error);
    return [];
  }
}

// 更新排行榜数据
async function updateLeaderboard(env, difficulty, username, time) {
  try {
    const leaderboard = await getLeaderboard(env, difficulty);

    // 移除同用户名的旧记录
    const filtered = leaderboard.filter(record => record.username !== username);

    // 添加新记录
    filtered.push({
      username: username.trim().substring(0, 20), // 限制长度
      time: parseInt(time),
      timestamp: Date.now()
    });

    // 按时间排序，保留前10名
    const sorted = filtered.sort((a, b) => a.time - b.time).slice(0, 10);

    await env.LEADERBOARD.put(`leaderboard:${difficulty}`, JSON.stringify(sorted));
    return sorted;
  } catch (error) {
    console.error('更新排行榜失败:', error);
    throw error;
  }
}

// 验证成绩数据
function validateScore(username, time, difficulty) {
  // 用户名验证：1-20字符，只允许字母、数字、中文、下划线、连字符
  if (!username || username.length === 0 || username.length > 20) {
    return { valid: false, error: '用户名长度必须在1-20字符之间' };
  }

  if (!/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/.test(username)) {
    return { valid: false, error: '用户名只能包含字母、数字、中文、下划线和连字符' };
  }

  // 时间验证：合理范围内的正整数
  const minTimes = { beginner: 1, intermediate: 10, expert: 30 };
  const maxTimes = { beginner: 999, intermediate: 9999, expert: 9999 };

  if (!Number.isInteger(time) || time < minTimes[difficulty] || time > maxTimes[difficulty]) {
    return { valid: false, error: '成绩数据异常，请重新游戏' };
  }

  return { valid: true };
}

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    // 处理排行榜API路由
    if (url.pathname.startsWith('/api/leaderboard/')) {
      return handleLeaderboardAPI(request, env, url);
    }

    if (url.pathname === '/') {
      return new Response(getGameHTML(), {
        headers: {
          'Content-Type': 'text/html;charset=UTF-8',
          'Cache-Control': 'public, max-age=3600',
        },
      });
    }

    return new Response('Not Found', { status: 404 });
  },
};

function getGameHTML() {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经典扫雷 - Classic Minesweeper</title>
    <style>
        /* 经典扫雷样式 - 完美复刻Windows风格 */
        :root {
            --cell-size: 30px;
            --counter-font-size: 24px;
            --smiley-size: 40px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'MS Sans Serif', sans-serif;
            background-color: #c0c0c0;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            user-select: none;
            margin: 0;
            padding: 20px 10px;
            box-sizing: border-box;
        }

        .main-container {
            display: flex;
            gap: 20px;
            width: 100%;
            max-width: 1600px;
            align-items: flex-start;
            justify-content: center;
        }

        .game-container {
            flex: 1;
            min-width: 400px;
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 10px;
            border-radius: 0;
            box-shadow: none;
            display: flex;
            flex-direction: column;
            align-items: center;
        }



        .difficulty-selector {
            margin-bottom: 1px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .difficulty-buttons {
            display: flex;
            gap: 5px;
        }

        .difficulty-button {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            min-width: 60px;
        }

        .difficulty-button:active {
            border: 2px inset #c0c0c0;
        }

        .difficulty-button.active {
            border: 2px inset #c0c0c0;
            background-color: #a0a0a0;
        }

        .help-button {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
        }

        .help-button:active {
            border: 2px inset #c0c0c0;
        }

        .help-button:hover {
            background-color: #d0d0d0;
        }

        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #c0c0c0;
            border: 2px inset #c0c0c0;
            padding: 3px 6px;
            margin-bottom: 1px;
            width: 100%;
        }

        .counter {
            background-color: #000;
            color: #ff0000;
            font-family: 'Courier New', monospace;
            font-size: var(--counter-font-size);
            font-weight: bold;
            padding: 3px 6px;
            border: 1px inset #808080;
            min-width: calc(var(--counter-font-size) * 2.5);
            text-align: center;
        }

        .smiley-button {
            width: var(--smiley-size);
            height: var(--smiley-size);
            font-size: calc(var(--smiley-size) * 0.6);
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .smiley-button:active {
            border: 2px inset #c0c0c0;
        }

        .game-board {
            border: 2px inset #c0c0c0;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #c0c0c0;
            padding: 2px;
            margin-top: 1px;
        }

        .board-grid {
            display: grid;
            gap: 0;
            border: 1px solid #808080;
            min-width: 200px;
            min-height: 200px;
        }

        .cell {
            width: var(--cell-size);
            height: var(--cell-size);
            border: 1px outset #c0c0c0;
            background-color: #c0c0c0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: calc(var(--cell-size) * 0.6);
            font-weight: bold;
            cursor: pointer;
            position: relative;
            min-width: 20px;
            min-height: 20px;
        }

        .cell:active {
            border: 1px inset #c0c0c0;
        }

        .cell.revealed {
            border: 1px solid #808080;
            background-color: #ffffff;
        }

        .cell.mine {
            background-color: #ff0000;
        }

        .cell.flagged::after {
            content: '🚩';
            font-size: calc(var(--cell-size) * 0.7);
        }

        .cell.number-1 { color: #0000ff; }
        .cell.number-2 { color: #008000; }
        .cell.number-3 { color: #ff0000; }
        .cell.number-4 { color: #000080; }
        .cell.number-5 { color: #800000; }
        .cell.number-6 { color: #008080; }
        .cell.number-7 { color: #000000; }
        .cell.number-8 { color: #808080; }

        .cell.quick-dig-highlight {
            background-color: #ffff99 !important;
            border: 2px inset #ffff00 !important;
        }

        /* 排行榜面板样式 */
        .leaderboard-panel {
            width: 280px;
            min-width: 280px;
            flex-shrink: 0;
            background-color: #c0c0c0;
            border: 2px inset #c0c0c0;
            padding: 10px;
            height: fit-content;
            max-height: 80vh;
            overflow-y: auto;
            display: block; /* 恢复显示 */
        }

        .leaderboard-header {
            margin-bottom: 15px;
        }

        .leaderboard-header h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            text-align: center;
            color: #000080;
        }

        .leaderboard-tabs {
            display: flex;
            gap: 2px;
        }

        .tab-button {
            flex: 1;
            padding: 6px 4px;
            font-size: 11px;
            background: #c0c0c0;
            border: 1px outset #c0c0c0;
            cursor: pointer;
            font-family: 'MS Sans Serif', sans-serif;
        }

        .tab-button:hover {
            background: #d0d0d0;
        }

        .tab-button.active {
            background: #ffffff;
            border: 1px inset #c0c0c0;
        }

        .leaderboard-content {
            position: relative;
            min-height: 200px;
        }

        .leaderboard-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 30px 10px;
            color: #666;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ddd;
            border-top: 2px solid #666;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .leaderboard-list {
            display: none;
        }

        .leaderboard-list.show {
            display: block;
        }

        .leaderboard-item {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            margin: 2px 0;
            background: #f0f0f0;
            border: 1px solid #808080;
            font-size: 12px;
        }

        .leaderboard-item.highlight {
            background: #ffff99;
            border-color: #ff8000;
        }

        .leaderboard-rank {
            font-weight: bold;
            color: #666;
            min-width: 25px;
            text-align: center;
        }

        .leaderboard-rank.gold { color: #ffd700; }
        .leaderboard-rank.silver { color: #c0c0c0; }
        .leaderboard-rank.bronze { color: #cd7f32; }

        .leaderboard-username {
            flex: 1;
            margin: 0 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-weight: bold;
        }

        .leaderboard-time {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #d00;
            font-size: 11px;
        }

        .leaderboard-empty {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .empty-text {
            font-size: 12px;
            line-height: 1.4;
        }

        .leaderboard-toggle {
            display: none;
            padding: 6px 10px;
            font-size: 14px;
            background: #c0c0c0;
            border: 1px outset #c0c0c0;
            cursor: pointer;
            margin-left: 10px;
        }

        .leaderboard-toggle:hover {
            background: #d0d0d0;
        }

        .header-controls {
            display: flex;
            align-items: center;
        }

        /* 游戏结束提示框样式 */
        .game-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .game-modal.show {
            opacity: 1;
        }

        .modal-content {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 25px;
            text-align: center;
            box-shadow: 4px 4px 12px rgba(0, 0, 0, 0.4);
            max-width: 420px;
            width: 90%;
            transform: scale(0.8);
            transition: transform 0.3s ease;
        }

        .game-modal.show .modal-content {
            transform: scale(1);
        }

        .modal-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #000080;
        }

        .modal-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .modal-message {
            font-size: 16px;
            margin-bottom: 20px;
            line-height: 1.4;
        }

        .modal-button {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 8px 20px;
            font-size: 14px;
            cursor: pointer;
            margin: 0 5px;
        }

        .modal-button:active {
            border: 2px inset #c0c0c0;
        }

        .modal-button:hover {
            background-color: #d0d0d0;
        }

        .modal-button.primary {
            background-color: #0078d4;
            color: white;
            border-color: #0078d4;
        }

        .modal-button.primary:hover {
            background-color: #106ebe;
        }

        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        /* 上传对话框样式 */
        .input-group {
            margin: 20px 0;
            text-align: left;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .input-group input {
            width: 100%;
            padding: 8px 12px;
            border: 2px inset #c0c0c0;
            background: white;
            font-size: 14px;
            font-family: 'MS Sans Serif', sans-serif;
            box-sizing: border-box;
        }

        .input-group input:focus {
            outline: none;
            border-color: #0078d4;
        }

        .input-hint {
            font-size: 11px;
            color: #666;
            margin-top: 4px;
        }

        .upload-info {
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 10px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .button-spinner {
            width: 14px;
            height: 14px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 8px;
        }



        /* 超大屏幕 - 显示排行榜 */
        @media (min-width: 1200px) {
            :root {
                --cell-size: 35px;
                --counter-font-size: 28px;
                --smiley-size: 45px;
            }

            .leaderboard-panel {
                display: block !important;
            }

            .leaderboard-toggle {
                display: none;
            }
        }

        /* 大屏幕 - 桌面端 */
        @media (min-width: 1000px) and (max-width: 1199px) {
            :root {
                --cell-size: 32px;
                --counter-font-size: 26px;
                --smiley-size: 42px;
            }

            .leaderboard-panel {
                display: block !important;
            }

            .leaderboard-toggle {
                display: inline-block;
            }
        }

        /* 中等屏幕 - 平板端 */
        @media (min-width: 768px) and (max-width: 999px) {
            :root {
                --cell-size: 30px;
                --counter-font-size: 24px;
                --smiley-size: 40px;
            }

            .leaderboard-panel {
                display: none;
            }

            .leaderboard-toggle {
                display: inline-block;
            }
        }

        /* 小屏幕 - 手机端 */
        @media (max-width: 767px) {
            :root {
                --cell-size: 28px;
                --counter-font-size: 22px;
                --smiley-size: 38px;
            }

            body {
                padding: 10px 5px;
            }

            .leaderboard-panel {
                display: none;
            }

            .leaderboard-toggle {
                display: inline-block;
            }
        }

        /* 超小屏幕 */
        @media (max-width: 480px) {
            :root {
                --cell-size: 25px;
                --counter-font-size: 20px;
                --smiley-size: 35px;
            }

            body {
                padding: 5px;
            }

            .leaderboard-panel {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 排行榜面板 -->
        <div class="leaderboard-panel" id="leaderboard-panel">
            <div class="leaderboard-header">
                <h3>🏆 排行榜</h3>
                <div class="leaderboard-tabs">
                    <button class="tab-button active" data-difficulty="beginner" onclick="switchLeaderboardDifficulty('beginner')">初级</button>
                    <button class="tab-button" data-difficulty="intermediate" onclick="switchLeaderboardDifficulty('intermediate')">中级</button>
                    <button class="tab-button" data-difficulty="expert" onclick="switchLeaderboardDifficulty('expert')">专家</button>
                </div>
            </div>
            <div class="leaderboard-content">
                <div class="leaderboard-loading" id="leaderboard-loading">
                    <div class="loading-spinner"></div>
                    <span>加载中...</span>
                </div>
                <div class="leaderboard-list" id="leaderboard-list">
                    <!-- 动态生成排行榜项目 -->
                </div>
                <div class="leaderboard-empty" id="leaderboard-empty" style="display: none;">
                    <div class="empty-icon">🎯</div>
                    <div class="empty-text">暂无记录<br>快来创造第一个记录吧！</div>
                </div>
            </div>
        </div>

        <!-- 游戏区域 -->
        <div class="game-container">
            <div class="difficulty-selector">
                <div class="difficulty-buttons">
                    <button class="difficulty-button active" onclick="setDifficulty('beginner')">初级</button>
                    <button class="difficulty-button" onclick="setDifficulty('intermediate')">中级</button>
                    <button class="difficulty-button" onclick="setDifficulty('expert')">专家</button>
                </div>
                <div class="header-controls">
                    <button class="help-button" onclick="showHelp()">怎么玩</button>
                    <button class="leaderboard-toggle" id="leaderboard-toggle" onclick="toggleLeaderboard()" style="display: none;">📊 排行榜</button>
                </div>
            </div>

            <div class="game-header">
                <div class="counter" id="mine-counter">010</div>
                <button class="smiley-button" id="smiley-button" onclick="newGame()">😊</button>
                <div class="counter" id="timer">000</div>
            </div>

            <div class="game-board">
                <div class="board-grid" id="board-grid"></div>
            </div>
        </div>
    </div>

    <!-- 游戏结束提示框 -->
    <div class="game-modal" id="game-modal">
        <div class="modal-content">
            <div class="modal-icon" id="modal-icon">🎉</div>
            <div class="modal-title" id="modal-title">恭喜胜利！</div>
            <div class="modal-message" id="modal-message">
                用时：120秒<br>
                难度：初级
            </div>
            <div class="modal-buttons">
                <button class="modal-button" onclick="closeModal()">确定</button>
                <button class="modal-button" onclick="newGameFromModal()">新游戏</button>
                <button class="modal-button primary" id="upload-score-btn" onclick="showUploadDialog()" style="display: none;">🏆 上传成绩</button>
            </div>
        </div>
    </div>

    <!-- 成绩上传对话框 -->
    <div class="game-modal" id="upload-modal">
        <div class="modal-content">
            <div class="modal-icon">🏆</div>
            <div class="modal-title">上传成绩</div>
            <div class="modal-message">
                <div class="upload-info" id="upload-info">
                    用时：120秒<br>
                    难度：初级
                </div>
                <div class="input-group">
                    <label for="username-input">请输入您的用户名：</label>
                    <input type="text" id="username-input" placeholder="1-20个字符" maxlength="20" autocomplete="off">
                    <div class="input-hint">只能包含字母、数字、中文、下划线和连字符</div>
                </div>
            </div>
            <div class="modal-buttons">
                <button class="modal-button" onclick="closeUploadModal()">取消</button>
                <button class="modal-button primary" onclick="submitScore()">
                    <span id="submit-text">提交</span>
                    <div class="button-spinner" id="submit-spinner" style="display: none;"></div>
                </button>
            </div>
        </div>
    </div>

    <!-- 怎么玩提示框 -->
    <div class="game-modal" id="help-modal">
        <div class="modal-content">
            <div class="modal-icon">🎯</div>
            <div class="modal-title">怎么玩</div>
            <div class="modal-message" style="text-align: left; line-height: 1.6;">
                <strong>🎯 游戏目标：</strong><br>
                找出所有地雷而不踩到它们！<br><br>

                <strong>🖱️ 操作方法：</strong><br>
                • 左键：挖掘格子<br>
                • 右键：标记地雷<br>
                • 双键：在已揭开的数字上同时按左右键，快速挖掘周围格子<br>
                &nbsp;&nbsp;（当标记数等于数字时生效）<br><br>

                <strong>📱 移动端：</strong><br>
                长按格子标记地雷<br><br>

                <strong>🏆 难度选择：</strong><br>
                • 初级：9×9，10个地雷<br>
                • 中级：16×16，40个地雷<br>
                • 专家：30×16，99个地雷<br><br>

                <strong>💡 提示：</strong><br>
                数字表示周围8个格子中地雷的数量
            </div>
            <button class="modal-button" onclick="closeHelpModal()">知道了</button>
        </div>
    </div>

    <script>
        // 经典扫雷游戏逻辑
        class MinesweeperGame {
            constructor() {
                this.difficulties = {
                    beginner: { rows: 9, cols: 9, mines: 10 },
                    intermediate: { rows: 16, cols: 16, mines: 40 },
                    expert: { rows: 16, cols: 30, mines: 99 }
                };

                this.currentDifficulty = 'beginner';
                this.board = [];
                this.revealed = [];
                this.flagged = [];
                this.gameState = 'ready'; // ready, playing, won, lost
                this.firstClick = true;
                this.startTime = null;
                this.timer = null;
                this.mineCount = 0;
                this.flagCount = 0;

                // 双键快速挖掘状态
                this.mouseButtons = { left: false, right: false };
                this.quickDigCell = null;

                // 性能优化：状态跟踪机制
                this.previousCellStates = []; // 记录上次的格子状态
                this.changedCells = new Set(); // 记录发生变化的格子
                this.updatePending = false; // 防止重复的更新请求

                this.initGame();
            }

            initGame() {
                const config = this.difficulties[this.currentDifficulty];
                this.rows = config.rows;
                this.cols = config.cols;
                this.mineCount = config.mines;
                this.flagCount = 0;

                // 初始化游戏板
                this.board = Array(this.rows).fill().map(() => Array(this.cols).fill(0));
                this.revealed = Array(this.rows).fill().map(() => Array(this.cols).fill(false));
                this.flagged = Array(this.rows).fill().map(() => Array(this.cols).fill(false));

                // 初始化状态跟踪
                this.previousCellStates = Array(this.rows).fill().map(() => Array(this.cols).fill(null));
                this.changedCells.clear();
                this.updatePending = false;

                this.gameState = 'ready';
                this.firstClick = true;
                this.startTime = null;

                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }

                this.createBoard();
                // 首次初始化时强制全量更新
                this.updateDisplay(true);

                // 重置显示
                document.getElementById('smiley-button').textContent = '😊';
                document.getElementById('timer').textContent = '000';
            }

            createBoard() {
                const boardGrid = document.getElementById('board-grid');

                // 完全清除现有内容和样式
                boardGrid.innerHTML = '';
                boardGrid.removeAttribute('style');
                boardGrid.className = '';

                // 强制重新计算CSS变量（确保使用当前游戏尺寸）
                this.calculateOptimalCellSize();

                // 等待CSS变量更新完成
                let cellSize;
                let attempts = 0;
                const maxAttempts = 10;

                do {
                    // 强制重新计算样式
                    document.body.offsetHeight;
                    document.documentElement.offsetHeight;

                    // 获取CSS变量值
                    cellSize = getComputedStyle(document.documentElement).getPropertyValue('--cell-size').trim();
                    attempts++;

                    // 如果获取到了有效值就跳出
                    if (cellSize && cellSize !== '30px' && cellSize !== '') {
                        break;
                    }

                    // 如果多次尝试失败，手动计算格子大小
                    if (attempts >= maxAttempts) {
                        const viewportWidth = window.innerWidth;
                        const viewportHeight = window.innerHeight;
                        const availableWidth = viewportWidth - 120;
                        const availableHeight = viewportHeight - 200;
                        const optimalSize = Math.min(
                            Math.floor(availableWidth / this.cols),
                            Math.floor(availableHeight / this.rows)
                        );
                        cellSize = Math.max(20, Math.min(45, optimalSize)) + 'px';
                        break;
                    }
                } while (attempts < maxAttempts);

                // 直接设置网格布局
                boardGrid.style.display = 'grid';
                boardGrid.style.gridTemplateColumns = 'repeat(' + this.cols + ', ' + cellSize + ')';
                boardGrid.style.gridTemplateRows = 'repeat(' + this.rows + ', ' + cellSize + ')';
                boardGrid.style.gap = '0';
                boardGrid.style.border = '1px solid #808080';

                // 多重强制重新渲染
                boardGrid.offsetHeight;
                boardGrid.offsetWidth;

                // 强制重新计算布局
                boardGrid.style.display = 'none';
                boardGrid.offsetHeight;
                boardGrid.style.display = 'grid';

                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        const cell = document.createElement('div');
                        cell.className = 'cell';
                        cell.dataset.row = row;
                        cell.dataset.col = col;

                        cell.addEventListener('click', (e) => this.handleLeftClick(row, col, e));
                        cell.addEventListener('contextmenu', (e) => this.handleRightClick(row, col, e));

                        // 双键快速挖掘支持
                        cell.addEventListener('mousedown', (e) => this.handleMouseDown(row, col, e));
                        cell.addEventListener('mouseup', (e) => this.handleMouseUp(row, col, e));

                        // 移动端长按支持
                        let touchTimer = null;
                        cell.addEventListener('touchstart', (e) => {
                            touchTimer = setTimeout(() => {
                                this.handleRightClick(row, col, e);
                            }, 500);
                        });

                        cell.addEventListener('touchend', (e) => {
                            if (touchTimer) {
                                clearTimeout(touchTimer);
                                touchTimer = null;
                            }
                        });

                        cell.addEventListener('touchmove', (e) => {
                            if (touchTimer) {
                                clearTimeout(touchTimer);
                                touchTimer = null;
                            }
                        });

                        boardGrid.appendChild(cell);
                    }
                }
            }

            // 计算最佳格子大小 - 确保完整适应视口，考虑排行榜占用空间
            calculateOptimalCellSize() {
                // 获取视口尺寸
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // 确保使用当前游戏的行列数
                const currentRows = this.rows || 9;
                const currentCols = this.cols || 9;

                // 动态获取排行榜面板的实际占用宽度
                const leaderboardPanel = document.querySelector('.leaderboard-panel');
                let leaderboardWidth = 0;

                if (leaderboardPanel && window.getComputedStyle(leaderboardPanel).display !== 'none') {
                    // 获取排行榜面板的实际宽度，包括边距和边框
                    const rect = leaderboardPanel.getBoundingClientRect();
                    const computedStyle = window.getComputedStyle(leaderboardPanel);
                    const marginLeft = parseFloat(computedStyle.marginLeft) || 0;
                    const marginRight = parseFloat(computedStyle.marginRight) || 0;
                    leaderboardWidth = rect.width + marginLeft + marginRight;
                }

                // 精确测量实际固定元素的高度
                const difficultySelector = document.querySelector('.difficulty-selector');
                const gameHeader = document.querySelector('.game-header');
                const mainContainer = document.querySelector('.main-container');

                let actualFixedHeight = 70; // 默认值
                if (difficultySelector && gameHeader) {
                    actualFixedHeight = difficultySelector.offsetHeight + gameHeader.offsetHeight;

                    // 添加容器的内边距
                    if (mainContainer) {
                        const containerStyle = window.getComputedStyle(mainContainer);
                        const paddingTop = parseFloat(containerStyle.paddingTop) || 0;
                        const paddingBottom = parseFloat(containerStyle.paddingBottom) || 0;
                        actualFixedHeight += paddingTop + paddingBottom;
                    }

                    // 添加安全边距
                    actualFixedHeight += 30;
                }

                // 计算游戏区域的实际可用空间
                const gameContainer = document.querySelector('.game-container');
                let horizontalMargins = 40; // 默认边距

                if (gameContainer) {
                    const containerStyle = window.getComputedStyle(gameContainer);
                    const marginLeft = parseFloat(containerStyle.marginLeft) || 0;
                    const marginRight = parseFloat(containerStyle.marginRight) || 0;
                    const paddingLeft = parseFloat(containerStyle.paddingLeft) || 0;
                    const paddingRight = parseFloat(containerStyle.paddingRight) || 0;
                    horizontalMargins = marginLeft + marginRight + paddingLeft + paddingRight + 20; // 额外安全边距
                }

                const availableWidth = viewportWidth - leaderboardWidth - horizontalMargins;
                const availableHeight = viewportHeight - actualFixedHeight;

                // 边界检查 - 确保可用空间合理
                if (availableWidth < 200 || availableHeight < 150) {
                    console.warn('可用空间过小，使用最小布局');
                    return this.calculateCellSizeWithWidth(
                        Math.max(200, availableWidth),
                        Math.max(150, availableHeight),
                        currentRows,
                        currentCols
                    );
                }

                // 检查专家级是否需要隐藏排行榜
                if (this.currentDifficulty === 'expert' && leaderboardWidth > 0 && availableWidth < 800) {
                    this.autoHideLeaderboard();
                    // 重新计算可用宽度（不包括排行榜）
                    const newAvailableWidth = viewportWidth - horizontalMargins;
                    return this.calculateCellSizeWithWidth(newAvailableWidth, availableHeight, currentRows, currentCols);
                }

                return this.calculateCellSizeWithWidth(availableWidth, availableHeight, currentRows, currentCols);
            }

            // 辅助函数：根据可用宽度计算格子大小
            calculateCellSizeWithWidth(availableWidth, availableHeight, currentRows, currentCols) {
                // 边界检查 - 确保输入参数合理
                if (availableWidth <= 0 || availableHeight <= 0 || currentRows <= 0 || currentCols <= 0) {
                    console.warn('计算格子大小时参数无效:', { availableWidth, availableHeight, currentRows, currentCols });
                    return this.setDefaultCellSize();
                }

                // 基于可用空间计算格子大小
                const maxCellSizeByWidth = Math.floor(availableWidth / currentCols);
                const maxCellSizeByHeight = Math.floor(availableHeight / currentRows);

                // 取较小值确保完整显示
                let optimalSize = Math.min(maxCellSizeByWidth, maxCellSizeByHeight);

                // 设置合理的大小范围
                const minCellSize = 12;  // 最小可用大小
                const maxCellSize = 50;  // 最大合理大小
                optimalSize = Math.max(minCellSize, Math.min(maxCellSize, optimalSize));

                // 精确验证 - 确保游戏板不会超出可用空间
                const boardBorder = 2; // 游戏板边框
                const totalBoardWidth = optimalSize * currentCols + boardBorder;
                const totalBoardHeight = optimalSize * currentRows + boardBorder;

                // 如果总尺寸超出可用空间，进一步缩小
                if (totalBoardWidth > availableWidth || totalBoardHeight > availableHeight) {
                    const scaleX = availableWidth / totalBoardWidth;
                    const scaleY = availableHeight / totalBoardHeight;
                    const scale = Math.min(scaleX, scaleY) * 0.95; // 留5%安全边距
                    optimalSize = Math.floor(optimalSize * scale);
                    optimalSize = Math.max(minCellSize, optimalSize);
                }

                // 最终边界检查
                if (optimalSize < minCellSize) {
                    console.warn('计算出的格子大小过小，使用默认值');
                    return this.setDefaultCellSize();
                }

                // 批量更新CSS变量（避免多次重排）
                this.updateCSSVariables(optimalSize);

                // 输出调试信息（仅在开发模式下）
                if (console.log) {
                    console.log('布局计算结果:');
                    console.log('  视口尺寸: ' + window.innerWidth + 'x' + window.innerHeight + 'px');
                    console.log('  可用空间: ' + availableWidth + 'x' + availableHeight + 'px');
                    console.log('  游戏尺寸: ' + currentCols + 'x' + currentRows);
                    console.log('  格子大小: ' + optimalSize + 'px');
                    console.log('  游戏板尺寸: ' + totalBoardWidth + 'x' + totalBoardHeight + 'px');
                }

                return optimalSize;
            }

            // 设置默认格子大小
            setDefaultCellSize() {
                const defaultSize = 25;
                this.updateCSSVariables(defaultSize);
                return defaultSize;
            }

            // 批量更新CSS变量，避免多次重排
            updateCSSVariables(cellSize) {
                const root = document.documentElement;

                // 计算相关尺寸
                const counterFontSize = Math.max(12, Math.floor(cellSize * 0.6));
                const smileySize = Math.max(24, Math.floor(cellSize * 1.0));

                // 批量设置CSS变量
                root.style.setProperty('--cell-size', cellSize + 'px');
                root.style.setProperty('--counter-font-size', counterFontSize + 'px');
                root.style.setProperty('--smiley-size', smileySize + 'px');

                // 使用requestAnimationFrame优化重排时机
                if (window.requestAnimationFrame) {
                    requestAnimationFrame(() => {
                        // 触发一次重排以应用新的CSS变量
                        root.offsetHeight;
                    });
                } else {
                    // 降级处理
                    root.offsetHeight;
                }
            }

            // 自动隐藏排行榜（当空间不足时）
            autoHideLeaderboard() {
                const leaderboardPanel = document.querySelector('.leaderboard-panel');
                const leaderboardToggle = document.querySelector('.leaderboard-toggle');

                if (leaderboardPanel) {
                    leaderboardPanel.style.display = 'none';
                    leaderboardPanel.classList.add('auto-hidden');
                }

                if (leaderboardToggle) {
                    leaderboardToggle.style.display = 'inline-block';
                }
            }

            // 生成地雷 - Fisher-Yates洗牌算法
            generateMines(firstClickRow, firstClickCol) {
                const positions = [];

                // 创建所有可能的位置（除了首次点击位置及其周围）
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        // 跳过首次点击位置及其周围8个格子
                        if (Math.abs(row - firstClickRow) <= 1 && Math.abs(col - firstClickCol) <= 1) {
                            continue;
                        }
                        positions.push([row, col]);
                    }
                }

                // Fisher-Yates洗牌
                for (let i = positions.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [positions[i], positions[j]] = [positions[j], positions[i]];
                }

                // 放置地雷
                for (let i = 0; i < this.mineCount && i < positions.length; i++) {
                    const [row, col] = positions[i];
                    this.board[row][col] = -1; // -1 表示地雷
                }

                // 计算数字
                this.calculateNumbers();
            }

            // 计算每个格子周围的地雷数
            calculateNumbers() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (this.board[row][col] !== -1) {
                            let count = 0;
                            for (let dr = -1; dr <= 1; dr++) {
                                for (let dc = -1; dc <= 1; dc++) {
                                    const newRow = row + dr;
                                    const newCol = col + dc;
                                    if (this.isValidCell(newRow, newCol) && this.board[newRow][newCol] === -1) {
                                        count++;
                                    }
                                }
                            }
                            this.board[row][col] = count;
                        }
                    }
                }
            }

            // 检查坐标是否有效
            isValidCell(row, col) {
                return row >= 0 && row < this.rows && col >= 0 && col < this.cols;
            }

            // 左键点击处理
            handleLeftClick(row, col, event) {
                event.preventDefault();

                if (this.gameState === 'won' || this.gameState === 'lost') return;
                if (this.flagged[row][col]) return;

                // 首次点击
                if (this.firstClick) {
                    this.generateMines(row, col);
                    this.firstClick = false;
                    this.gameState = 'playing';
                    this.startTimer();
                    document.getElementById('smiley-button').textContent = '😊';
                }

                this.revealCell(row, col);
                this.updateDisplay();
                this.checkGameState();
            }

            // 右键点击处理
            handleRightClick(row, col, event) {
                event.preventDefault();

                if (this.gameState === 'won' || this.gameState === 'lost') return;
                if (this.revealed[row][col]) return;

                this.flagged[row][col] = !this.flagged[row][col];
                this.flagCount += this.flagged[row][col] ? 1 : -1;

                this.updateDisplay();
            }

            // 鼠标按下处理（用于双键快速挖掘）
            handleMouseDown(row, col, event) {
                if (this.gameState === 'won' || this.gameState === 'lost') return;

                if (event.button === 0) { // 左键
                    this.mouseButtons.left = true;
                } else if (event.button === 2) { // 右键
                    this.mouseButtons.right = true;
                }

                // 检查是否双键按下
                if (this.mouseButtons.left && this.mouseButtons.right) {
                    this.quickDigCell = { row, col };
                    this.highlightQuickDigArea(row, col, true);
                }
            }

            // 鼠标释放处理
            handleMouseUp(row, col, event) {
                if (this.gameState === 'won' || this.gameState === 'lost') return;

                const wasQuickDig = this.mouseButtons.left && this.mouseButtons.right;

                if (event.button === 0) { // 左键释放
                    this.mouseButtons.left = false;
                } else if (event.button === 2) { // 右键释放
                    this.mouseButtons.right = false;
                }

                // 执行快速挖掘
                if (wasQuickDig && this.quickDigCell &&
                    this.quickDigCell.row === row && this.quickDigCell.col === col) {
                    this.performQuickDig(row, col);
                }

                // 清除高亮
                if (this.quickDigCell) {
                    this.highlightQuickDigArea(this.quickDigCell.row, this.quickDigCell.col, false);
                    this.quickDigCell = null;
                }
            }

            // 高亮快速挖掘区域
            highlightQuickDigArea(row, col, highlight) {
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        const newRow = row + dr;
                        const newCol = col + dc;
                        if (this.isValidCell(newRow, newCol) && !this.revealed[newRow][newCol]) {
                            const cellIndex = newRow * this.cols + newCol;
                            const cellElement = document.querySelectorAll('.cell')[cellIndex];
                            if (cellElement) {
                                if (highlight) {
                                    cellElement.classList.add('quick-dig-highlight');
                                } else {
                                    cellElement.classList.remove('quick-dig-highlight');
                                }
                            }
                        }
                    }
                }
            }

            // 执行快速挖掘
            performQuickDig(row, col) {
                // 只有已揭开的数字格子才能进行快速挖掘
                if (!this.revealed[row][col] || this.board[row][col] <= 0) {
                    return;
                }

                const targetNumber = this.board[row][col];
                let flaggedCount = 0;

                // 计算周围标记的地雷数
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        const newRow = row + dr;
                        const newCol = col + dc;
                        if (this.isValidCell(newRow, newCol) && this.flagged[newRow][newCol]) {
                            flaggedCount++;
                        }
                    }
                }

                // 只有当标记数等于目标数字时才能快速挖掘
                if (flaggedCount === targetNumber) {
                    for (let dr = -1; dr <= 1; dr++) {
                        for (let dc = -1; dc <= 1; dc++) {
                            const newRow = row + dr;
                            const newCol = col + dc;
                            if (this.isValidCell(newRow, newCol) &&
                                !this.revealed[newRow][newCol] &&
                                !this.flagged[newRow][newCol]) {
                                this.revealCell(newRow, newCol);
                            }
                        }
                    }
                    this.updateDisplay();
                    this.checkGameState();
                }
            }

            // 揭开格子
            revealCell(row, col) {
                if (!this.isValidCell(row, col) || this.revealed[row][col] || this.flagged[row][col]) {
                    return;
                }

                this.revealed[row][col] = true;

                // 如果是地雷，游戏结束
                if (this.board[row][col] === -1) {
                    this.gameState = 'lost';
                    this.revealAllMines();
                    return;
                }

                // 如果是空格子（数字为0），自动展开周围
                if (this.board[row][col] === 0) {
                    for (let dr = -1; dr <= 1; dr++) {
                        for (let dc = -1; dc <= 1; dc++) {
                            this.revealCell(row + dr, col + dc);
                        }
                    }
                }
            }

            // 揭开所有地雷（游戏失败时）
            revealAllMines() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (this.board[row][col] === -1) {
                            this.revealed[row][col] = true;
                        }
                    }
                }
            }

            // 检查游戏状态
            checkGameState() {
                if (this.gameState === 'lost') {
                    document.getElementById('smiley-button').textContent = '😵';
                    this.stopTimer();

                    // 显示失败提示
                    setTimeout(() => {
                        this.showGameModal('💣', '游戏失败！', '踩到地雷了！\\n点击笑脸或新游戏按钮重新开始。');
                    }, 500);
                    return;
                }

                // 检查是否胜利
                let unrevealedCount = 0;
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (!this.revealed[row][col] && this.board[row][col] !== -1) {
                            unrevealedCount++;
                        }
                    }
                }

                if (unrevealedCount === 0) {
                    this.gameState = 'won';
                    document.getElementById('smiley-button').textContent = '😎';
                    this.stopTimer();

                    // 自动标记所有地雷
                    for (let row = 0; row < this.rows; row++) {
                        for (let col = 0; col < this.cols; col++) {
                            if (this.board[row][col] === -1 && !this.flagged[row][col]) {
                                this.flagged[row][col] = true;
                                this.flagCount++;
                            }
                        }
                    }

                    // 显示胜利提示
                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                    setTimeout(() => {
                        const message = '用时：' + elapsed + '秒\\n难度：' + this.getDifficultyName() + '\\n\\n太棒了！你成功找出了所有地雷！';
                        this.showGameModal('🎉', '恭喜胜利！', message, elapsed, this.currentDifficulty);
                    }, 500);
                }
            }

            // 获取难度名称
            getDifficultyName() {
                const names = {
                    'beginner': '初级',
                    'intermediate': '中级',
                    'expert': '专家'
                };
                return names[this.currentDifficulty] || '自定义';
            }

            // 显示游戏结束提示框
            showGameModal(icon, title, message, time = null, difficulty = null) {
                const modal = document.getElementById('game-modal');
                const uploadBtn = document.getElementById('upload-score-btn');

                document.getElementById('modal-icon').textContent = icon;
                document.getElementById('modal-title').textContent = title;
                document.getElementById('modal-message').innerHTML = message.replace(/\\n/g, '<br>');

                // 如果是胜利且有时间和难度，显示上传按钮
                if (time !== null && difficulty !== null) {
                    uploadBtn.style.display = 'inline-block';
                    // 保存成绩信息供上传使用
                    window.currentScore = { time, difficulty };
                } else {
                    uploadBtn.style.display = 'none';
                    window.currentScore = null;
                }

                modal.style.display = 'flex';
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10);
            }

            // 开始计时器
            startTimer() {
                this.startTime = Date.now();
                this.timer = setInterval(() => {
                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                    const displayTime = Math.min(elapsed, 999);
                    document.getElementById('timer').textContent = displayTime.toString().padStart(3, '0');
                }, 1000);
            }

            // 停止计时器
            stopTimer() {
                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
            }

            // 更新显示 - 优化版本，支持增量更新
            updateDisplay(forceFullUpdate = false) {
                // 更新地雷计数器
                const remainingMines = this.mineCount - this.flagCount;
                document.getElementById('mine-counter').textContent =
                    Math.max(-99, Math.min(999, remainingMines)).toString().padStart(3, '0');

                // 防止重复的更新请求
                if (this.updatePending && !forceFullUpdate) {
                    return;
                }

                this.updatePending = true;

                // 使用requestAnimationFrame优化更新时机
                requestAnimationFrame(() => {
                    this.performDisplayUpdate(forceFullUpdate);
                    this.updatePending = false;
                });
            }

            // 执行实际的显示更新
            performDisplayUpdate(forceFullUpdate = false) {
                const cells = document.querySelectorAll('.cell');

                if (forceFullUpdate || this.previousCellStates.length === 0) {
                    // 全量更新（首次或强制更新）
                    this.performFullUpdate(cells);
                } else {
                    // 增量更新（只更新变化的格子）
                    this.performIncrementalUpdate(cells);
                }

                // 更新状态跟踪
                this.updateStateTracking();
            }

            // 执行全量更新
            performFullUpdate(cells) {
                cells.forEach((cell, index) => {
                    const row = Math.floor(index / this.cols);
                    const col = index % this.cols;
                    this.updateSingleCell(cell, row, col);
                });
            }

            // 执行增量更新
            performIncrementalUpdate(cells) {
                // 检测变化的格子
                this.detectChangedCells();

                // 只更新变化的格子
                this.changedCells.forEach(cellIndex => {
                    const row = Math.floor(cellIndex / this.cols);
                    const col = cellIndex % this.cols;
                    const cell = cells[cellIndex];
                    if (cell) {
                        this.updateSingleCell(cell, row, col);
                    }
                });

                // 清除变化记录
                this.changedCells.clear();
            }

            // 检测发生变化的格子
            detectChangedCells() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        const currentState = this.getCellState(row, col);
                        const previousState = this.previousCellStates[row][col];

                        if (currentState !== previousState) {
                            const cellIndex = row * this.cols + col;
                            this.changedCells.add(cellIndex);
                        }
                    }
                }
            }

            // 获取格子的当前状态（用于比较）
            getCellState(row, col) {
                if (this.flagged[row][col]) {
                    return 'flagged';
                } else if (this.revealed[row][col]) {
                    if (this.board[row][col] === -1) {
                        return 'mine';
                    } else if (this.board[row][col] > 0) {
                        return 'number-' + this.board[row][col];
                    } else {
                        return 'revealed-empty';
                    }
                } else {
                    return 'hidden';
                }
            }

            // 更新单个格子的显示
            updateSingleCell(cell, row, col) {
                // 清除所有状态类
                cell.className = 'cell';
                cell.textContent = '';

                if (this.flagged[row][col]) {
                    cell.classList.add('flagged');
                } else if (this.revealed[row][col]) {
                    cell.classList.add('revealed');

                    if (this.board[row][col] === -1) {
                        cell.classList.add('mine');
                        cell.textContent = '💣';
                    } else if (this.board[row][col] > 0) {
                        cell.classList.add('number-' + this.board[row][col]);
                        cell.textContent = this.board[row][col];
                    }
                }
            }

            // 更新状态跟踪
            updateStateTracking() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        this.previousCellStates[row][col] = this.getCellState(row, col);
                    }
                }
            }
        }

        // 排行榜管理器
        class LeaderboardManager {
            constructor(game) {
                this.game = game;
                this.currentDifficulty = 'beginner';
                this.cache = {}; // 简单缓存
                this.isLoading = false;
            }

            async loadLeaderboard(difficulty) {
                if (this.isLoading) return;

                this.isLoading = true;
                this.showLoading();

                try {
                    // 检查缓存
                    if (this.cache[difficulty]) {
                        this.updateDisplay(this.cache[difficulty]);
                        this.hideLoading();
                        this.isLoading = false;
                        return;
                    }

                    const response = await fetch('/api/leaderboard/' + difficulty);
                    const result = await response.json();

                    if (result.success) {
                        this.cache[difficulty] = result.data;
                        this.updateDisplay(result.data);
                    } else {
                        this.showError('加载排行榜失败');
                    }
                } catch (error) {
                    console.error('加载排行榜错误:', error);
                    this.showError('网络连接失败');
                } finally {
                    this.hideLoading();
                    this.isLoading = false;
                }
            }

            async uploadScore(username, time, difficulty) {
                try {
                    const response = await fetch('/api/leaderboard/' + difficulty, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ username, time })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 更新缓存和显示
                        this.cache[difficulty] = result.data;
                        if (this.currentDifficulty === difficulty) {
                            this.updateDisplay(result.data, username);
                        }
                        return { success: true };
                    } else {
                        return { success: false, error: result.error };
                    }
                } catch (error) {
                    console.error('上传成绩错误:', error);
                    return { success: false, error: '网络连接失败' };
                }
            }

            switchDifficulty(difficulty) {
                this.currentDifficulty = difficulty;

                // 更新标签页状态
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.difficulty === difficulty) {
                        btn.classList.add('active');
                    }
                });

                // 加载对应难度的排行榜
                this.loadLeaderboard(difficulty);
            }

            updateDisplay(data, highlightUsername = null) {
                const listElement = document.getElementById('leaderboard-list');
                const emptyElement = document.getElementById('leaderboard-empty');

                if (data.length === 0) {
                    listElement.style.display = 'none';
                    emptyElement.style.display = 'block';
                    return;
                }

                listElement.style.display = 'block';
                emptyElement.style.display = 'none';

                listElement.innerHTML = data.map((record, index) => {
                    const rank = index + 1;
                    const rankClass = rank === 1 ? 'gold' : rank === 2 ? 'silver' : rank === 3 ? 'bronze' : '';
                    const highlightClass = highlightUsername && record.username === highlightUsername ? 'highlight' : '';

                    return '<div class="leaderboard-item ' + highlightClass + '"><div class="leaderboard-rank ' + rankClass + '">' + rank + '</div><div class="leaderboard-username">' + this.escapeHtml(record.username) + '</div><div class="leaderboard-time">' + record.time + 's</div></div>';
                }).join('');
            }

            showLoading() {
                document.getElementById('leaderboard-loading').style.display = 'flex';
                document.getElementById('leaderboard-list').style.display = 'none';
                document.getElementById('leaderboard-empty').style.display = 'none';
            }

            hideLoading() {
                document.getElementById('leaderboard-loading').style.display = 'none';
            }

            showError(message) {
                const listElement = document.getElementById('leaderboard-list');
                listElement.innerHTML = '<div style="text-align: center; color: #d00; padding: 20px;">' + message + '</div>';
                listElement.style.display = 'block';
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // 清除指定难度的缓存
            clearCache(difficulty) {
                if (difficulty) {
                    delete this.cache[difficulty];
                } else {
                    this.cache = {};
                }
            }
        }

        // 全局游戏实例
        let game = new MinesweeperGame();

        // 全局排行榜管理器
        window.leaderboardManager = new LeaderboardManager(game);

        // 全局鼠标事件监听（处理双键快速挖掘）
        document.addEventListener('mouseup', (e) => {
            if (game.mouseButtons.left || game.mouseButtons.right) {
                game.mouseButtons.left = false;
                game.mouseButtons.right = false;

                if (game.quickDigCell) {
                    game.highlightQuickDigArea(game.quickDigCell.row, game.quickDigCell.col, false);
                    game.quickDigCell = null;
                }
            }
        });

        // 防止右键菜单
        document.addEventListener('contextmenu', (e) => {
            if (e.target.classList.contains('cell')) {
                e.preventDefault();
            }
        });

        // 窗口大小变化时重新计算布局
        window.addEventListener('resize', () => {
            if (game) {
                game.calculateOptimalCellSize();
            }
        });

        // 点击提示框背景关闭
        document.getElementById('game-modal').addEventListener('click', (e) => {
            if (e.target.id === 'game-modal') {
                closeModal();
            }
        });

        // 点击帮助提示框背景关闭
        document.getElementById('help-modal').addEventListener('click', (e) => {
            if (e.target.id === 'help-modal') {
                closeHelpModal();
            }
        });
        
        // 工具函数

        // 防抖函数 - 优化窗口大小变化事件处理
        function debounce(func, wait, immediate = false) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    timeout = null;
                    if (!immediate) func.apply(this, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(this, args);
            };
        }

        // 节流函数 - 限制函数执行频率
        function throttle(func, limit) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }

        // 窗口大小变化处理函数
        function handleWindowResize() {
            if (game && game.calculateOptimalCellSize) {
                console.log('窗口大小变化，重新计算布局');
                game.calculateOptimalCellSize();
            }
        }

        // 创建防抖版本的resize处理函数
        const debouncedResize = debounce(handleWindowResize, 250);

        // 创建节流版本的resize处理函数（用于即时反馈）
        const throttledResize = throttle(() => {
            // 立即更新排行榜显示状态，提供即时反馈
            updateLayoutForDifficulty(game.currentDifficulty);
        }, 100);

        function setDifficulty(difficulty) {
            // 更新按钮状态
            document.querySelectorAll('.difficulty-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 先更新难度
            game.currentDifficulty = difficulty;

            // 同步切换排行榜难度
            if (window.leaderboardManager) {
                window.leaderboardManager.switchDifficulty(difficulty);
            }

            // 获取新难度的配置
            const config = game.difficulties[difficulty];

            // 立即更新游戏尺寸
            game.rows = config.rows;
            game.cols = config.cols;
            game.mineCount = config.mines;

            // 检查是否需要调整排行榜显示
            updateLayoutForDifficulty(difficulty);

            // 立即清空游戏板
            const boardGrid = document.getElementById('board-grid');
            boardGrid.innerHTML = '';
            boardGrid.removeAttribute('style');
            boardGrid.className = '';

            // 强制重新计算CSS变量
            game.calculateOptimalCellSize();

            // 多次强制刷新确保CSS变量生效
            for (let i = 0; i < 5; i++) {
                document.body.offsetHeight;
                document.documentElement.offsetHeight;
            }

            // 使用多重异步确保完全更新
            setTimeout(() => {
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        // 完整重新初始化游戏
                        game.initGame();
                    }, 10);
                });
            }, 10);
        }

        // 根据难度调整布局
        function updateLayoutForDifficulty(difficulty) {
            const leaderboardPanel = document.querySelector('.leaderboard-panel');
            const leaderboardToggle = document.querySelector('.leaderboard-toggle');
            const viewportWidth = window.innerWidth;

            // 确保排行榜在大屏幕上显示
            if (viewportWidth >= 1000) {
                if (leaderboardPanel) {
                    leaderboardPanel.style.display = 'block';
                }
                if (leaderboardToggle) {
                    leaderboardToggle.style.display = 'none';
                }
            } else {
                // 小屏幕时显示切换按钮
                if (leaderboardToggle) {
                    leaderboardToggle.style.display = 'inline-block';
                }
            }

            // 使用防抖机制重新计算布局，避免频繁调用
            if (game && game.calculateOptimalCellSize) {
                debouncedResize();
            }
        }
        
        function newGame() {
            game.initGame();
        }
        
        function showHelp() {
            const modal = document.getElementById('help-modal');
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        // 关闭提示框
        function closeModal() {
            const modal = document.getElementById('game-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 从提示框开始新游戏
        function newGameFromModal() {
            closeModal();
            newGame();
        }

        // 关闭帮助提示框
        function closeHelpModal() {
            const modal = document.getElementById('help-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 排行榜相关函数
        function switchLeaderboardDifficulty(difficulty) {
            if (window.leaderboardManager) {
                window.leaderboardManager.switchDifficulty(difficulty);
            }
        }

        function toggleLeaderboard() {
            const leaderboardPanel = document.querySelector('.leaderboard-panel');
            const toggleBtn = document.getElementById('leaderboard-toggle');

            if (leaderboardPanel) {
                if (leaderboardPanel.style.display === 'none') {
                    leaderboardPanel.style.display = 'block';
                    toggleBtn.textContent = '❌ 隐藏排行榜';
                    // 加载排行榜数据
                    if (window.leaderboardManager) {
                        window.leaderboardManager.loadLeaderboard('beginner');
                    }
                } else {
                    leaderboardPanel.style.display = 'none';
                    toggleBtn.textContent = '📊 排行榜';
                }
                // 使用防抖机制重新计算布局
                debouncedResize();
            }
        }

        // 成绩上传相关函数
        function showUploadDialog() {
            if (!window.currentScore) return;

            const modal = document.getElementById('upload-modal');
            const uploadInfo = document.getElementById('upload-info');
            const usernameInput = document.getElementById('username-input');

            // 更新上传信息显示
            const difficultyNames = {
                'beginner': '初级',
                'intermediate': '中级',
                'expert': '专家'
            };

            uploadInfo.innerHTML = '用时：' + window.currentScore.time + '秒<br>难度：' + difficultyNames[window.currentScore.difficulty];

            // 清空输入框
            usernameInput.value = '';
            usernameInput.focus();

            // 显示对话框
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        function closeUploadModal() {
            const modal = document.getElementById('upload-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        async function submitScore() {
            const usernameInput = document.getElementById('username-input');
            const submitBtn = document.querySelector('#upload-modal .modal-button.primary');
            const submitText = document.getElementById('submit-text');
            const submitSpinner = document.getElementById('submit-spinner');

            const username = usernameInput.value.trim();

            // 验证用户名
            if (!username) {
                alert('请输入用户名');
                usernameInput.focus();
                return;
            }

            if (username.length > 20) {
                alert('用户名不能超过20个字符');
                usernameInput.focus();
                return;
            }

            if (!/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/.test(username)) {
                alert('用户名只能包含字母、数字、中文、下划线和连字符');
                usernameInput.focus();
                return;
            }

            // 显示加载状态
            submitBtn.disabled = true;
            submitText.style.display = 'none';
            submitSpinner.style.display = 'inline-block';

            try {
                const result = await window.leaderboardManager.uploadScore(
                    username,
                    window.currentScore.time,
                    window.currentScore.difficulty
                );

                if (result.success) {
                    // 上传成功
                    closeUploadModal();
                    closeModal();
                    alert('🎉 成绩上传成功！\n快去排行榜看看你的排名吧！');
                } else {
                    // 上传失败
                    alert('上传失败：' + (result.error || '未知错误'));
                }
            } catch (error) {
                console.error('提交成绩错误:', error);
                alert('上传失败：网络连接错误');
            } finally {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitText.style.display = 'inline';
                submitSpinner.style.display = 'none';
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 初始化排行榜
            if (window.leaderboardManager) {
                window.leaderboardManager.loadLeaderboard('beginner');
            }

            // 确保游戏正确初始化
            if (game) {
                game.initGame();
            }

            // 添加窗口大小变化事件监听器
            window.addEventListener('resize', () => {
                // 使用节流函数提供即时反馈
                throttledResize();
                // 使用防抖函数进行最终的精确计算
                debouncedResize();
            });

            // 添加方向变化事件监听器（移动设备）
            window.addEventListener('orientationchange', () => {
                // 方向变化后稍微延迟，等待浏览器完成布局调整
                setTimeout(() => {
                    debouncedResize();
                }, 300);
            });

            console.log('窗口大小变化监听器已初始化');
        });

        // 支持回车键提交成绩
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                const uploadModal = document.getElementById('upload-modal');
                if (uploadModal && uploadModal.style.display === 'flex') {
                    submitScore();
                }
            }
        });
    </script>
</body>
</html>`;
}
