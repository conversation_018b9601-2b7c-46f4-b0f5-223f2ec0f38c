# 排行榜功能部署指南

## 🚀 快速部署步骤

### 1. 创建 KV Namespace

首先需要在 Cloudflare 中创建 KV namespace：

```bash
# 创建生产环境的 KV namespace
npx wrangler kv:namespace create "LEADERBOARD"

# 创建预览环境的 KV namespace  
npx wrangler kv:namespace create "LEADERBOARD" --preview
```

### 2. 更新 wrangler.toml 配置

将命令输出的 namespace ID 更新到 `wrangler.toml` 文件中：

```toml
name = "mines"
main = "src/index.js"
compatibility_date = "2024-01-01"

# KV存储配置 - 排行榜数据
[[kv_namespaces]]
binding = "LEADERBOARD"
id = "your-actual-kv-namespace-id"        # 替换为实际的 namespace ID
preview_id = "your-preview-kv-namespace-id"  # 替换为实际的预览 namespace ID

[env.production]
name = "mines"
```

### 3. 部署到 Cloudflare Workers

```bash
# 部署到生产环境
npm run deploy
```

## 🎮 功能特性

### ✅ 已实现功能

- **🏆 三难度排行榜**：初级、中级、专家级独立排行榜
- **📱 响应式设计**：自适应各种屏幕尺寸
- **🎯 智能布局**：专家级自动调整排行榜显示避免遮挡
- **⚡ 实时更新**：成绩上传后立即更新排行榜显示
- **🔒 数据验证**：前后端双重验证确保数据安全
- **💫 用户体验**：加载状态、错误提示、成功反馈

### 🎨 界面特点

- **左侧排行榜面板**：固定280px宽度，显示前10名
- **智能切换**：难度切换时同步更新排行榜
- **高亮显示**：新上传的成绩会高亮显示
- **排名标识**：前三名有特殊颜色标识（金银铜）

### 📊 数据存储

- **存储格式**：JSON数组存储在 Cloudflare KV
- **键值规则**：`leaderboard:{difficulty}` (如 `leaderboard:beginner`)
- **数据结构**：
  ```json
  [
    {"username": "玩家1", "time": 120, "timestamp": 1640995200000},
    {"username": "玩家2", "time": 135, "timestamp": 1640995300000}
  ]
  ```

## 🔧 技术细节

### API 路由

- `GET /api/leaderboard/{difficulty}` - 获取排行榜
- `POST /api/leaderboard/{difficulty}` - 上传成绩

### 响应式断点

- **超大屏幕** (≥1400px)：显示排行榜
- **大屏幕** (1000px-1399px)：显示排行榜，可切换
- **中等屏幕** (768px-999px)：排行榜移至底部
- **小屏幕** (<768px)：排行榜改为模态框

### 数据验证规则

- **用户名**：1-20字符，只允许字母、数字、中文、下划线、连字符
- **时间范围**：
  - 初级：1-999秒
  - 中级：10-9999秒  
  - 专家：30-9999秒

## 🐛 故障排除

### 常见问题

1. **排行榜不显示**
   - 检查 KV namespace 是否正确配置
   - 确认 namespace ID 是否正确

2. **上传失败**
   - 检查网络连接
   - 确认用户名格式是否正确

3. **布局异常**
   - 刷新页面重新计算布局
   - 检查浏览器控制台是否有错误

### 调试信息

游戏会在浏览器控制台输出布局计算信息，包括：
- 视口尺寸
- 可用空间
- 游戏板尺寸
- 格子大小

## 🎯 使用说明

1. **查看排行榜**：点击左侧排行榜标签切换不同难度
2. **上传成绩**：游戏胜利后点击"🏆 上传成绩"按钮
3. **输入用户名**：在弹出框中输入用户名并提交
4. **查看排名**：成功上传后会在排行榜中高亮显示

---

🎮 享受您的扫雷排行榜功能吧！
