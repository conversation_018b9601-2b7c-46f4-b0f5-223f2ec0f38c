export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    if (url.pathname === '/') {
      return new Response(`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .game-board {
            display: grid;
            grid-template-columns: repeat(9, 30px);
            gap: 1px;
            margin: 20px 0;
            justify-content: center;
        }
        .cell {
            width: 30px;
            height: 30px;
            background: #c0c0c0;
            border: 2px outset #c0c0c0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: bold;
        }
        .cell:active {
            border: 2px inset #c0c0c0;
        }
        .cell.revealed {
            background: white;
            border: 1px solid #808080;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>扫雷游戏测试</h1>
        <p>这是一个简化的测试版本，用于验证基础功能。</p>
        
        <div>
            <button onclick="initGame()">新游戏</button>
            <button onclick="testFunction()">测试功能</button>
        </div>
        
        <div class="game-board" id="game-board">
            <!-- 游戏板将在这里生成 -->
        </div>
        
        <div id="status">状态：等待开始</div>
    </div>

    <script>
        console.log('页面加载完成');
        
        let board = [];
        let rows = 9;
        let cols = 9;
        
        function initGame() {
            console.log('初始化游戏');
            document.getElementById('status').textContent = '状态：游戏开始';
            
            // 创建游戏板
            const gameBoard = document.getElementById('game-board');
            gameBoard.innerHTML = '';
            
            for (let i = 0; i < rows * cols; i++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.textContent = '';
                cell.onclick = () => cellClick(i);
                gameBoard.appendChild(cell);
            }
        }
        
        function cellClick(index) {
            console.log('点击格子:', index);
            const cells = document.querySelectorAll('.cell');
            const cell = cells[index];
            cell.classList.add('revealed');
            cell.textContent = Math.floor(Math.random() * 8) + 1;
            document.getElementById('status').textContent = '状态：点击了格子 ' + index;
        }
        
        function testFunction() {
            console.log('测试功能被调用');
            alert('测试功能正常工作！');
            document.getElementById('status').textContent = '状态：测试功能已执行';
        }
        
        // 页面加载后自动初始化
        window.addEventListener('DOMContentLoaded', () => {
            console.log('DOM加载完成，自动初始化游戏');
            initGame();
        });
    </script>
</body>
</html>`, {
        headers: {
          'Content-Type': 'text/html;charset=UTF-8',
        },
      });
    }

    return new Response('Not Found', { status: 404 });
  },
};
