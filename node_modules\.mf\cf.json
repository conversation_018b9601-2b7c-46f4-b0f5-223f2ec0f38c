{"clientTcpRtt": 404, "requestHeaderNames": {}, "httpProtocol": "HTTP/1.1", "tlsCipher": "AEAD-AES256-GCM-SHA384", "continent": "AS", "asn": 4134, "clientAcceptEncoding": "br, gzip, deflate", "verifiedBotCategory": "", "country": "CN", "isEUCountry": false, "region": "Shanghai", "tlsClientCiphersSha1": "JZtiTn8H/ntxORk+XXvU2EvNoz8=", "tlsClientAuth": {"certIssuerDNLegacy": "", "certIssuerSKI": "", "certSubjectDNRFC2253": "", "certSubjectDNLegacy": "", "certFingerprintSHA256": "", "certNotBefore": "", "certSKI": "", "certSerial": "", "certIssuerDN": "", "certVerified": "NONE", "certNotAfter": "", "certSubjectDN": "", "certPresented": "0", "certRevoked": "0", "certIssuerSerial": "", "certIssuerDNRFC2253": "", "certFingerprintSHA1": ""}, "tlsClientRandom": "bEV2WnPVCRk42L7vnTgZV9KwtbwZ0OL2paizDT+5Jxk=", "tlsExportedAuthenticator": {"clientFinished": "92ec426d17c32afe545a58e37531b7bc8630ea46d846644a3f783dc3f078127453119ad6b148933dbca43f6466d04c6f", "clientHandshake": "6ebeb91a1285ecdc05504059b4f84e67bb3eaf41e093b1c582179af805d82aa49f3ee7a35b7e3aa17e016f59d19cea53", "serverHandshake": "96670b702940f11dc38dc6c88ff32df8bd88483cb304b09282d322da44346ad27f1833c66fc94fe10fd2f6d545b8e217", "serverFinished": "d7c2f2914c9c279bb9a2b0ecc3c8b3b2b27d712a687ccdd0ca3e7af247a2e7590fd673060de84b15a201d00d91c670ce"}, "tlsClientHelloLength": "386", "colo": "SJC", "timezone": "Asia/Shanghai", "longitude": "121.45806", "latitude": "31.22222", "edgeRequestKeepAliveStatus": 1, "requestPriority": "", "postalCode": "200000", "city": "Shanghai", "tlsVersion": "TLSv1.3", "regionCode": "SH", "asOrganization": "CHINANET Guangdong Province Network", "tlsClientExtensionsSha1Le": "6e+q3vPm88rSgMTN/h7WTTxQ2wQ=", "tlsClientExtensionsSha1": "Y7DIC8A6G0/aXviZ8ie/xDbJb7g=", "botManagement": {"corporateProxy": false, "verifiedBot": false, "jsDetection": {"passed": false}, "staticResource": false, "detectionIds": {}, "score": 99}}