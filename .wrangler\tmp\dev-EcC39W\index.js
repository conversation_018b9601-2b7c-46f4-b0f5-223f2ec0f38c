var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-hQIGZL/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// .wrangler/tmp/bundle-hQIGZL/strip-cf-connecting-ip-header.js
function stripCfConnectingIPHeader(input, init) {
  const request = new Request(input, init);
  request.headers.delete("CF-Connecting-IP");
  return request;
}
__name(stripCfConnectingIPHeader, "stripCfConnectingIPHeader");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    return Reflect.apply(target, thisArg, [
      stripCfConnectingIPHeader.apply(null, argArray)
    ]);
  }
});

// src/index.js
async function handleLeaderboardAPI(request, env, url) {
  const pathParts = url.pathname.split("/");
  const difficulty = pathParts[3];
  if (!["beginner", "intermediate", "expert"].includes(difficulty)) {
    return new Response(JSON.stringify({ success: false, error: "\u65E0\u6548\u7684\u96BE\u5EA6\u7EA7\u522B" }), {
      status: 400,
      headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
    });
  }
  try {
    if (request.method === "GET") {
      const leaderboard = await getLeaderboard(env, difficulty);
      return new Response(JSON.stringify({ success: true, data: leaderboard }), {
        headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
      });
    } else if (request.method === "POST") {
      const body = await request.json();
      const { username, time } = body;
      const validation = validateScore(username, time, difficulty);
      if (!validation.valid) {
        return new Response(JSON.stringify({ success: false, error: validation.error }), {
          status: 400,
          headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
        });
      }
      const updatedLeaderboard = await updateLeaderboard(env, difficulty, username, time);
      return new Response(JSON.stringify({ success: true, data: updatedLeaderboard }), {
        headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
      });
    } else if (request.method === "OPTIONS") {
      return new Response(null, {
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type"
        }
      });
    }
  } catch (error) {
    console.error("\u6392\u884C\u699CAPI\u9519\u8BEF:", error);
    return new Response(JSON.stringify({ success: false, error: "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF" }), {
      status: 500,
      headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
    });
  }
  return new Response("Method Not Allowed", { status: 405 });
}
__name(handleLeaderboardAPI, "handleLeaderboardAPI");
async function getLeaderboard(env, difficulty) {
  try {
    const data = await env.LEADERBOARD.get("leaderboard:" + difficulty);
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error("\u83B7\u53D6\u6392\u884C\u699C\u5931\u8D25:", error);
    return [];
  }
}
__name(getLeaderboard, "getLeaderboard");
async function updateLeaderboard(env, difficulty, username, time) {
  try {
    const leaderboard = await getLeaderboard(env, difficulty);
    const filtered = leaderboard.filter((record) => record.username !== username);
    filtered.push({
      username: username.trim().substring(0, 20),
      // 限制长度
      time: parseInt(time),
      timestamp: Date.now()
    });
    const sorted = filtered.sort((a, b) => a.time - b.time).slice(0, 10);
    await env.LEADERBOARD.put("leaderboard:" + difficulty, JSON.stringify(sorted));
    return sorted;
  } catch (error) {
    console.error("\u66F4\u65B0\u6392\u884C\u699C\u5931\u8D25:", error);
    throw error;
  }
}
__name(updateLeaderboard, "updateLeaderboard");
function validateScore(username, time, difficulty) {
  if (!username || username.length === 0 || username.length > 20) {
    return { valid: false, error: "\u7528\u6237\u540D\u957F\u5EA6\u5FC5\u987B\u57281-20\u5B57\u7B26\u4E4B\u95F4" };
  }
  if (!/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/.test(username)) {
    return { valid: false, error: "\u7528\u6237\u540D\u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E2D\u6587\u3001\u4E0B\u5212\u7EBF\u548C\u8FDE\u5B57\u7B26" };
  }
  const minTimes = { beginner: 1, intermediate: 10, expert: 30 };
  const maxTimes = { beginner: 999, intermediate: 9999, expert: 9999 };
  if (!Number.isInteger(time) || time < minTimes[difficulty] || time > maxTimes[difficulty]) {
    return { valid: false, error: "\u6210\u7EE9\u6570\u636E\u5F02\u5E38\uFF0C\u8BF7\u91CD\u65B0\u6E38\u620F" };
  }
  return { valid: true };
}
__name(validateScore, "validateScore");
var src_default = {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    if (url.pathname.startsWith("/api/leaderboard/")) {
      return handleLeaderboardAPI(request, env, url);
    }
    if (url.pathname === "/") {
      return new Response(getGameHTML(), {
        headers: {
          "Content-Type": "text/html;charset=UTF-8",
          "Cache-Control": "public, max-age=3600"
        }
      });
    }
    return new Response("Not Found", { status: 404 });
  }
};
function getGameHTML() {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>\u7ECF\u5178\u626B\u96F7 - Classic Minesweeper</title>
    <style>
        /* \u7ECF\u5178\u626B\u96F7\u6837\u5F0F - \u5B8C\u7F8E\u590D\u523BWindows\u98CE\u683C */
        :root {
            --cell-size: 30px;
            --counter-font-size: 24px;
            --smiley-size: 40px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'MS Sans Serif', sans-serif;
            background-color: #c0c0c0;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            user-select: none;
            margin: 0;
            padding: 20px 10px;
            box-sizing: border-box;
        }

        .main-container {
            display: flex;
            gap: 20px;
            width: 100%;
            max-width: 1600px;
            align-items: flex-start;
            justify-content: center;
        }

        .game-container {
            flex: 1;
            min-width: 400px;
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 10px;
            border-radius: 0;
            box-shadow: none;
            display: flex;
            flex-direction: column;
            align-items: center;
        }



        .difficulty-selector {
            margin-bottom: 1px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }

        .difficulty-buttons {
            display: flex;
            gap: 5px;
        }

        .difficulty-button {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            min-width: 60px;
        }

        .difficulty-button:active {
            border: 2px inset #c0c0c0;
        }

        .difficulty-button.active {
            border: 2px inset #c0c0c0;
            background-color: #a0a0a0;
        }

        .help-button {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
        }

        .help-button:active {
            border: 2px inset #c0c0c0;
        }

        .help-button:hover {
            background-color: #d0d0d0;
        }

        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #c0c0c0;
            border: 2px inset #c0c0c0;
            padding: 3px 6px;
            margin-bottom: 1px;
            width: 100%;
        }

        .counter {
            background-color: #000;
            color: #ff0000;
            font-family: 'Courier New', monospace;
            font-size: var(--counter-font-size);
            font-weight: bold;
            padding: 3px 6px;
            border: 1px inset #808080;
            min-width: calc(var(--counter-font-size) * 2.5);
            text-align: center;
        }

        .smiley-button {
            width: var(--smiley-size);
            height: var(--smiley-size);
            font-size: calc(var(--smiley-size) * 0.6);
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .smiley-button:active {
            border: 2px inset #c0c0c0;
        }

        .game-board {
            border: 2px inset #c0c0c0;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #c0c0c0;
            padding: 2px;
            margin-top: 1px;
        }

        .board-grid {
            display: grid;
            gap: 0;
            border: 1px solid #808080;
            min-width: 200px;
            min-height: 200px;
        }

        .cell {
            width: var(--cell-size);
            height: var(--cell-size);
            border: 1px outset #c0c0c0;
            background-color: #c0c0c0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: calc(var(--cell-size) * 0.6);
            font-weight: bold;
            cursor: pointer;
            position: relative;
            min-width: 20px;
            min-height: 20px;
        }

        .cell:active {
            border: 1px inset #c0c0c0;
        }

        .cell.revealed {
            border: 1px solid #808080;
            background-color: #ffffff;
        }

        .cell.mine {
            background-color: #ff0000;
        }

        .cell.flagged::after {
            content: '\u{1F6A9}';
            font-size: calc(var(--cell-size) * 0.7);
        }

        .cell.number-1 { color: #0000ff; }
        .cell.number-2 { color: #008000; }
        .cell.number-3 { color: #ff0000; }
        .cell.number-4 { color: #000080; }
        .cell.number-5 { color: #800000; }
        .cell.number-6 { color: #008080; }
        .cell.number-7 { color: #000000; }
        .cell.number-8 { color: #808080; }

        .cell.quick-dig-highlight {
            background-color: #ffff99 !important;
            border: 2px inset #ffff00 !important;
        }

        /* \u6392\u884C\u699C\u9762\u677F\u6837\u5F0F */
        .leaderboard-panel {
            width: 280px;
            min-width: 280px;
            flex-shrink: 0;
            background-color: #c0c0c0;
            border: 2px inset #c0c0c0;
            padding: 10px;
            height: fit-content;
            max-height: 80vh;
            overflow-y: auto;
            display: block; /* \u6062\u590D\u663E\u793A */
        }

        .leaderboard-header {
            margin-bottom: 15px;
        }

        .leaderboard-header h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            text-align: center;
            color: #000080;
        }

        .leaderboard-tabs {
            display: flex;
            gap: 2px;
        }

        .tab-button {
            flex: 1;
            padding: 6px 4px;
            font-size: 11px;
            background: #c0c0c0;
            border: 1px outset #c0c0c0;
            cursor: pointer;
            font-family: 'MS Sans Serif', sans-serif;
        }

        .tab-button:hover {
            background: #d0d0d0;
        }

        .tab-button.active {
            background: #ffffff;
            border: 1px inset #c0c0c0;
        }

        .leaderboard-content {
            position: relative;
            min-height: 200px;
        }

        .leaderboard-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 30px 10px;
            color: #666;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ddd;
            border-top: 2px solid #666;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .leaderboard-list {
            display: none;
        }

        .leaderboard-list.show {
            display: block;
        }

        .leaderboard-item {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            margin: 2px 0;
            background: #f0f0f0;
            border: 1px solid #808080;
            font-size: 12px;
        }

        .leaderboard-item.highlight {
            background: #ffff99;
            border-color: #ff8000;
        }

        .leaderboard-rank {
            font-weight: bold;
            color: #666;
            min-width: 25px;
            text-align: center;
        }

        .leaderboard-rank.gold { color: #ffd700; }
        .leaderboard-rank.silver { color: #c0c0c0; }
        .leaderboard-rank.bronze { color: #cd7f32; }

        .leaderboard-username {
            flex: 1;
            margin: 0 8px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-weight: bold;
        }

        .leaderboard-time {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #d00;
            font-size: 11px;
        }

        .leaderboard-empty {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 32px;
            margin-bottom: 10px;
        }

        .empty-text {
            font-size: 12px;
            line-height: 1.4;
        }

        .leaderboard-toggle {
            display: none;
            padding: 6px 10px;
            font-size: 14px;
            background: #c0c0c0;
            border: 1px outset #c0c0c0;
            cursor: pointer;
            margin-left: 10px;
        }

        .leaderboard-toggle:hover {
            background: #d0d0d0;
        }

        .header-controls {
            display: flex;
            align-items: center;
        }

        /* \u6E38\u620F\u7ED3\u675F\u63D0\u793A\u6846\u6837\u5F0F */
        .game-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .game-modal.show {
            opacity: 1;
        }

        .modal-content {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 25px;
            text-align: center;
            box-shadow: 4px 4px 12px rgba(0, 0, 0, 0.4);
            max-width: 420px;
            width: 90%;
            transform: scale(0.8);
            transition: transform 0.3s ease;
        }

        .game-modal.show .modal-content {
            transform: scale(1);
        }

        .modal-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #000080;
        }

        .modal-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .modal-message {
            font-size: 16px;
            margin-bottom: 20px;
            line-height: 1.4;
        }

        .modal-button {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 8px 20px;
            font-size: 14px;
            cursor: pointer;
            margin: 0 5px;
        }

        .modal-button:active {
            border: 2px inset #c0c0c0;
        }

        .modal-button:hover {
            background-color: #d0d0d0;
        }

        .modal-button.primary {
            background-color: #0078d4;
            color: white;
            border-color: #0078d4;
        }

        .modal-button.primary:hover {
            background-color: #106ebe;
        }

        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        /* \u4E0A\u4F20\u5BF9\u8BDD\u6846\u6837\u5F0F */
        .input-group {
            margin: 20px 0;
            text-align: left;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .input-group input {
            width: 100%;
            padding: 8px 12px;
            border: 2px inset #c0c0c0;
            background: white;
            font-size: 14px;
            font-family: 'MS Sans Serif', sans-serif;
            box-sizing: border-box;
        }

        .input-group input:focus {
            outline: none;
            border-color: #0078d4;
        }

        .input-hint {
            font-size: 11px;
            color: #666;
            margin-top: 4px;
        }

        .upload-info {
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 10px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .button-spinner {
            width: 14px;
            height: 14px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 8px;
        }

        /* \u53CC\u952E\u5FEB\u901F\u6316\u6398\u89C6\u89C9\u53CD\u9988\u6837\u5F0F */
        .cell.quick-dig-highlight {
            background: #ffff99 !important;
            border: 2px solid #ff8000 !important;
            box-shadow: 0 0 8px rgba(255, 128, 0, 0.6);
            animation: quickDigPulse 0.5s ease-in-out infinite alternate;
        }

        .cell.quick-dig-active {
            background: #90ee90 !important;
            border: 2px solid #32cd32 !important;
            transform: scale(1.05);
            transition: all 0.15s ease;
        }

        @keyframes quickDigPulse {
            0% {
                box-shadow: 0 0 8px rgba(255, 128, 0, 0.6);
                transform: scale(1);
            }
            100% {
                box-shadow: 0 0 12px rgba(255, 128, 0, 0.9);
                transform: scale(1.02);
            }
        }

        /* \u89E6\u6478\u8BBE\u5907\u4F18\u5316 */
        @media (hover: none) and (pointer: coarse) {
            .cell {
                min-height: 32px;
                min-width: 32px;
            }

            .cell.quick-dig-highlight {
                animation-duration: 0.3s;
            }
        }



        /* \u8D85\u5927\u5C4F\u5E55 - \u663E\u793A\u6392\u884C\u699C */
        @media (min-width: 1200px) {
            :root {
                --cell-size: 35px;
                --counter-font-size: 28px;
                --smiley-size: 45px;
            }

            .leaderboard-panel {
                display: block !important;
            }

            .leaderboard-toggle {
                display: none;
            }
        }

        /* \u5927\u5C4F\u5E55 - \u684C\u9762\u7AEF */
        @media (min-width: 1000px) and (max-width: 1199px) {
            :root {
                --cell-size: 32px;
                --counter-font-size: 26px;
                --smiley-size: 42px;
            }

            .leaderboard-panel {
                display: block !important;
            }

            .leaderboard-toggle {
                display: inline-block;
            }
        }

        /* \u4E2D\u7B49\u5C4F\u5E55 - \u5E73\u677F\u7AEF */
        @media (min-width: 768px) and (max-width: 999px) {
            :root {
                --cell-size: 30px;
                --counter-font-size: 24px;
                --smiley-size: 40px;
            }

            .leaderboard-panel {
                display: none;
            }

            .leaderboard-toggle {
                display: inline-block;
            }
        }

        /* \u5C0F\u5C4F\u5E55 - \u624B\u673A\u7AEF */
        @media (max-width: 767px) {
            :root {
                --cell-size: 28px;
                --counter-font-size: 22px;
                --smiley-size: 38px;
            }

            body {
                padding: 10px 5px;
            }

            .leaderboard-panel {
                display: none;
            }

            .leaderboard-toggle {
                display: inline-block;
            }
        }

        /* \u8D85\u5C0F\u5C4F\u5E55 */
        @media (max-width: 480px) {
            :root {
                --cell-size: 25px;
                --counter-font-size: 20px;
                --smiley-size: 35px;
            }

            body {
                padding: 5px;
            }

            .leaderboard-panel {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- \u6392\u884C\u699C\u9762\u677F -->
        <div class="leaderboard-panel" id="leaderboard-panel">
            <div class="leaderboard-header">
                <h3>\u{1F3C6} \u6392\u884C\u699C</h3>
                <div class="leaderboard-tabs">
                    <button class="tab-button active" data-difficulty="beginner" onclick="switchLeaderboardDifficulty('beginner')">\u521D\u7EA7</button>
                    <button class="tab-button" data-difficulty="intermediate" onclick="switchLeaderboardDifficulty('intermediate')">\u4E2D\u7EA7</button>
                    <button class="tab-button" data-difficulty="expert" onclick="switchLeaderboardDifficulty('expert')">\u4E13\u5BB6</button>
                </div>
            </div>
            <div class="leaderboard-content">
                <div class="leaderboard-loading" id="leaderboard-loading">
                    <div class="loading-spinner"></div>
                    <span>\u52A0\u8F7D\u4E2D...</span>
                </div>
                <div class="leaderboard-list" id="leaderboard-list">
                    <!-- \u52A8\u6001\u751F\u6210\u6392\u884C\u699C\u9879\u76EE -->
                </div>
                <div class="leaderboard-empty" id="leaderboard-empty" style="display: none;">
                    <div class="empty-icon">\u{1F3AF}</div>
                    <div class="empty-text">\u6682\u65E0\u8BB0\u5F55<br>\u5FEB\u6765\u521B\u9020\u7B2C\u4E00\u4E2A\u8BB0\u5F55\u5427\uFF01</div>
                </div>
            </div>
        </div>

        <!-- \u6E38\u620F\u533A\u57DF -->
        <div class="game-container">
            <div class="difficulty-selector">
                <div class="difficulty-buttons">
                    <button class="difficulty-button active" onclick="setDifficulty('beginner')">\u521D\u7EA7</button>
                    <button class="difficulty-button" onclick="setDifficulty('intermediate')">\u4E2D\u7EA7</button>
                    <button class="difficulty-button" onclick="setDifficulty('expert')">\u4E13\u5BB6</button>
                </div>
                <div class="header-controls">
                    <button class="help-button" onclick="showHelp()">\u600E\u4E48\u73A9</button>
                    <button class="leaderboard-toggle" id="leaderboard-toggle" onclick="toggleLeaderboard()" style="display: none;">\u{1F4CA} \u6392\u884C\u699C</button>
                </div>
            </div>

            <div class="game-header">
                <div class="counter" id="mine-counter">010</div>
                <button class="smiley-button" id="smiley-button" onclick="newGame()">\u{1F60A}</button>
                <div class="counter" id="timer">000</div>
            </div>

            <div class="game-board">
                <div class="board-grid" id="board-grid"></div>
            </div>
        </div>
    </div>

    <!-- \u6E38\u620F\u7ED3\u675F\u63D0\u793A\u6846 -->
    <div class="game-modal" id="game-modal">
        <div class="modal-content">
            <div class="modal-icon" id="modal-icon">\u{1F389}</div>
            <div class="modal-title" id="modal-title">\u606D\u559C\u80DC\u5229\uFF01</div>
            <div class="modal-message" id="modal-message">
                \u7528\u65F6\uFF1A120\u79D2<br>
                \u96BE\u5EA6\uFF1A\u521D\u7EA7
            </div>
            <div class="modal-buttons">
                <button class="modal-button" onclick="closeModal()">\u786E\u5B9A</button>
                <button class="modal-button" onclick="newGameFromModal()">\u65B0\u6E38\u620F</button>
                <button class="modal-button primary" id="upload-score-btn" onclick="showUploadDialog()" style="display: none;">\u{1F3C6} \u4E0A\u4F20\u6210\u7EE9</button>
            </div>
        </div>
    </div>

    <!-- \u6210\u7EE9\u4E0A\u4F20\u5BF9\u8BDD\u6846 -->
    <div class="game-modal" id="upload-modal">
        <div class="modal-content">
            <div class="modal-icon">\u{1F3C6}</div>
            <div class="modal-title">\u4E0A\u4F20\u6210\u7EE9</div>
            <div class="modal-message">
                <div class="upload-info" id="upload-info">
                    \u7528\u65F6\uFF1A120\u79D2<br>
                    \u96BE\u5EA6\uFF1A\u521D\u7EA7
                </div>
                <div class="input-group">
                    <label for="username-input">\u8BF7\u8F93\u5165\u60A8\u7684\u7528\u6237\u540D\uFF1A</label>
                    <input type="text" id="username-input" placeholder="1-20\u4E2A\u5B57\u7B26" maxlength="20" autocomplete="off">
                    <div class="input-hint">\u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E2D\u6587\u3001\u4E0B\u5212\u7EBF\u548C\u8FDE\u5B57\u7B26</div>
                </div>
            </div>
            <div class="modal-buttons">
                <button class="modal-button" onclick="closeUploadModal()">\u53D6\u6D88</button>
                <button class="modal-button primary" onclick="submitScore()">
                    <span id="submit-text">\u63D0\u4EA4</span>
                    <div class="button-spinner" id="submit-spinner" style="display: none;"></div>
                </button>
            </div>
        </div>
    </div>

    <!-- \u600E\u4E48\u73A9\u63D0\u793A\u6846 -->
    <div class="game-modal" id="help-modal">
        <div class="modal-content">
            <div class="modal-icon">\u{1F3AF}</div>
            <div class="modal-title">\u600E\u4E48\u73A9</div>
            <div class="modal-message" style="text-align: left; line-height: 1.6;">
                <strong>\u{1F3AF} \u6E38\u620F\u76EE\u6807\uFF1A</strong><br>
                \u627E\u51FA\u6240\u6709\u5730\u96F7\u800C\u4E0D\u8E29\u5230\u5B83\u4EEC\uFF01<br><br>

                <strong>\u{1F5B1}\uFE0F \u64CD\u4F5C\u65B9\u6CD5\uFF1A</strong><br>
                \u2022 \u5DE6\u952E\uFF1A\u6316\u6398\u683C\u5B50<br>
                \u2022 \u53F3\u952E\uFF1A\u6807\u8BB0\u5730\u96F7<br>
                \u2022 \u53CC\u952E\uFF1A\u5728\u5DF2\u63ED\u5F00\u7684\u6570\u5B57\u4E0A\u540C\u65F6\u6309\u5DE6\u53F3\u952E\uFF0C\u5FEB\u901F\u6316\u6398\u5468\u56F4\u683C\u5B50<br>
                &nbsp;&nbsp;\uFF08\u5F53\u6807\u8BB0\u6570\u7B49\u4E8E\u6570\u5B57\u65F6\u751F\u6548\uFF09<br><br>

                <strong>\u{1F4F1} \u79FB\u52A8\u7AEF\uFF1A</strong><br>
                \u957F\u6309\u683C\u5B50\u6807\u8BB0\u5730\u96F7<br><br>

                <strong>\u{1F3C6} \u96BE\u5EA6\u9009\u62E9\uFF1A</strong><br>
                \u2022 \u521D\u7EA7\uFF1A9\xD79\uFF0C10\u4E2A\u5730\u96F7<br>
                \u2022 \u4E2D\u7EA7\uFF1A16\xD716\uFF0C40\u4E2A\u5730\u96F7<br>
                \u2022 \u4E13\u5BB6\uFF1A30\xD716\uFF0C99\u4E2A\u5730\u96F7<br><br>

                <strong>\u{1F4A1} \u63D0\u793A\uFF1A</strong><br>
                \u6570\u5B57\u8868\u793A\u5468\u56F48\u4E2A\u683C\u5B50\u4E2D\u5730\u96F7\u7684\u6570\u91CF
            </div>
            <button class="modal-button" onclick="closeHelpModal()">\u77E5\u9053\u4E86</button>
        </div>
    </div>

    <script>
        // \u7ECF\u5178\u626B\u96F7\u6E38\u620F\u903B\u8F91
        class MinesweeperGame {
            constructor() {
                this.difficulties = {
                    beginner: { rows: 9, cols: 9, mines: 10 },
                    intermediate: { rows: 16, cols: 16, mines: 40 },
                    expert: { rows: 16, cols: 30, mines: 99 }
                };

                this.currentDifficulty = 'beginner';
                this.board = [];
                this.revealed = [];
                this.flagged = [];
                this.gameState = 'ready'; // ready, playing, won, lost
                this.firstClick = true;
                this.startTime = null;
                this.timer = null;
                this.mineCount = 0;
                this.flagCount = 0;

                // \u53CC\u952E\u5FEB\u901F\u6316\u6398\u72B6\u6001 - \u589E\u5F3A\u7248\u672C
                this.mouseButtons = { left: false, right: false };
                this.quickDigCell = null;
                this.mouseDownTimestamp = { left: 0, right: 0 };
                this.isQuickDigActive = false;
                this.touchSupport = {
                    longPressTimer: null,
                    isLongPress: false,
                    startTime: 0,
                    threshold: 500, // \u957F\u6309\u9608\u503C\uFF08\u6BEB\u79D2\uFF09
                    lastTapTime: 0,
                    lastTapRow: -1,
                    lastTapCol: -1
                };

                // \u6027\u80FD\u4F18\u5316\uFF1A\u72B6\u6001\u8DDF\u8E2A\u673A\u5236
                this.previousCellStates = []; // \u8BB0\u5F55\u4E0A\u6B21\u7684\u683C\u5B50\u72B6\u6001
                this.changedCells = new Set(); // \u8BB0\u5F55\u53D1\u751F\u53D8\u5316\u7684\u683C\u5B50
                this.updatePending = false; // \u9632\u6B62\u91CD\u590D\u7684\u66F4\u65B0\u8BF7\u6C42

                this.initGame();
            }

            initGame() {
                const config = this.difficulties[this.currentDifficulty];
                this.rows = config.rows;
                this.cols = config.cols;
                this.mineCount = config.mines;
                this.flagCount = 0;

                // \u521D\u59CB\u5316\u6E38\u620F\u677F
                this.board = Array(this.rows).fill().map(() => Array(this.cols).fill(0));
                this.revealed = Array(this.rows).fill().map(() => Array(this.cols).fill(false));
                this.flagged = Array(this.rows).fill().map(() => Array(this.cols).fill(false));

                // \u521D\u59CB\u5316\u72B6\u6001\u8DDF\u8E2A
                this.previousCellStates = Array(this.rows).fill().map(() => Array(this.cols).fill(null));
                this.changedCells.clear();
                this.updatePending = false;

                this.gameState = 'ready';
                this.firstClick = true;
                this.startTime = null;

                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }

                this.createBoard();
                // \u9996\u6B21\u521D\u59CB\u5316\u65F6\u5F3A\u5236\u5168\u91CF\u66F4\u65B0
                this.updateDisplay(true);

                // \u91CD\u7F6E\u663E\u793A
                document.getElementById('smiley-button').textContent = '\u{1F60A}';
                document.getElementById('timer').textContent = '000';
            }

            createBoard() {
                const boardGrid = document.getElementById('board-grid');

                // \u5B8C\u5168\u6E05\u9664\u73B0\u6709\u5185\u5BB9\u548C\u6837\u5F0F
                boardGrid.innerHTML = '';
                boardGrid.removeAttribute('style');
                boardGrid.className = '';

                // \u5F3A\u5236\u91CD\u65B0\u8BA1\u7B97CSS\u53D8\u91CF\uFF08\u786E\u4FDD\u4F7F\u7528\u5F53\u524D\u6E38\u620F\u5C3A\u5BF8\uFF09
                this.calculateOptimalCellSize();

                // \u7B49\u5F85CSS\u53D8\u91CF\u66F4\u65B0\u5B8C\u6210
                let cellSize;
                let attempts = 0;
                const maxAttempts = 10;

                do {
                    // \u5F3A\u5236\u91CD\u65B0\u8BA1\u7B97\u6837\u5F0F
                    document.body.offsetHeight;
                    document.documentElement.offsetHeight;

                    // \u83B7\u53D6CSS\u53D8\u91CF\u503C
                    cellSize = getComputedStyle(document.documentElement).getPropertyValue('--cell-size').trim();
                    attempts++;

                    // \u5982\u679C\u83B7\u53D6\u5230\u4E86\u6709\u6548\u503C\u5C31\u8DF3\u51FA
                    if (cellSize && cellSize !== '30px' && cellSize !== '') {
                        break;
                    }

                    // \u5982\u679C\u591A\u6B21\u5C1D\u8BD5\u5931\u8D25\uFF0C\u624B\u52A8\u8BA1\u7B97\u683C\u5B50\u5927\u5C0F
                    if (attempts >= maxAttempts) {
                        const viewportWidth = window.innerWidth;
                        const viewportHeight = window.innerHeight;
                        const availableWidth = viewportWidth - 120;
                        const availableHeight = viewportHeight - 200;
                        const optimalSize = Math.min(
                            Math.floor(availableWidth / this.cols),
                            Math.floor(availableHeight / this.rows)
                        );
                        cellSize = Math.max(20, Math.min(45, optimalSize)) + 'px';
                        break;
                    }
                } while (attempts < maxAttempts);

                // \u76F4\u63A5\u8BBE\u7F6E\u7F51\u683C\u5E03\u5C40
                boardGrid.style.display = 'grid';
                boardGrid.style.gridTemplateColumns = 'repeat(' + this.cols + ', ' + cellSize + ')';
                boardGrid.style.gridTemplateRows = 'repeat(' + this.rows + ', ' + cellSize + ')';
                boardGrid.style.gap = '0';
                boardGrid.style.border = '1px solid #808080';

                // \u591A\u91CD\u5F3A\u5236\u91CD\u65B0\u6E32\u67D3
                boardGrid.offsetHeight;
                boardGrid.offsetWidth;

                // \u5F3A\u5236\u91CD\u65B0\u8BA1\u7B97\u5E03\u5C40
                boardGrid.style.display = 'none';
                boardGrid.offsetHeight;
                boardGrid.style.display = 'grid';

                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        const cell = document.createElement('div');
                        cell.className = 'cell';
                        cell.dataset.row = row;
                        cell.dataset.col = col;

                        cell.addEventListener('click', (e) => this.handleLeftClick(row, col, e));
                        cell.addEventListener('contextmenu', (e) => this.handleRightClick(row, col, e));

                        // \u53CC\u952E\u5FEB\u901F\u6316\u6398\u652F\u6301
                        cell.addEventListener('mousedown', (e) => this.handleMouseDown(row, col, e));
                        cell.addEventListener('mouseup', (e) => this.handleMouseUp(row, col, e));

                        // \u79FB\u52A8\u7AEF\u589E\u5F3A\u89E6\u6478\u652F\u6301
                        let touchTimer = null;
                        let touchStartTime = 0;
                        let touchMoved = false;

                        cell.addEventListener('touchstart', (e) => {
                            touchStartTime = Date.now();
                            touchMoved = false;

                            // \u957F\u6309\u652F\u6301\uFF08\u6807\u8BB0\u5730\u96F7\uFF09
                            touchTimer = setTimeout(() => {
                                if (!touchMoved) {
                                    this.handleRightClick(row, col, e);
                                    // \u89E6\u89C9\u53CD\u9988\uFF08\u5982\u679C\u652F\u6301\uFF09
                                    if (navigator.vibrate) {
                                        navigator.vibrate(50);
                                    }
                                }
                            }, 500);
                        });

                        cell.addEventListener('touchmove', (e) => {
                            touchMoved = true;
                            if (touchTimer) {
                                clearTimeout(touchTimer);
                                touchTimer = null;
                            }
                        });

                        cell.addEventListener('touchend', (e) => {
                            const touchDuration = Date.now() - touchStartTime;

                            if (touchTimer) {
                                clearTimeout(touchTimer);
                                touchTimer = null;
                            }

                            // \u77ED\u6309\u5904\u7406
                            if (!touchMoved && touchDuration < 500) {
                                // \u68C0\u67E5\u662F\u5426\u662F\u53CC\u51FB\uFF08\u89E6\u6478\u8BBE\u5907\u7684\u5FEB\u901F\u6316\u6398\u66FF\u4EE3\u65B9\u6848\uFF09
                                if (this.touchSupport.lastTapTime &&
                                    Date.now() - this.touchSupport.lastTapTime < 300 &&
                                    this.touchSupport.lastTapRow === row &&
                                    this.touchSupport.lastTapCol === col) {

                                    // \u53CC\u51FB\u5FEB\u901F\u6316\u6398
                                    this.performQuickDig(row, col);
                                    this.touchSupport.lastTapTime = 0; // \u91CD\u7F6E
                                } else {
                                    // \u5355\u51FB\u5904\u7406
                                    this.handleLeftClick(row, col, e);
                                    this.touchSupport.lastTapTime = Date.now();
                                    this.touchSupport.lastTapRow = row;
                                    this.touchSupport.lastTapCol = col;
                                }
                            }
                        });

                        boardGrid.appendChild(cell);
                    }
                }
            }

            // \u8BA1\u7B97\u6700\u4F73\u683C\u5B50\u5927\u5C0F - \u786E\u4FDD\u5B8C\u6574\u9002\u5E94\u89C6\u53E3\uFF0C\u8003\u8651\u6392\u884C\u699C\u5360\u7528\u7A7A\u95F4
            calculateOptimalCellSize() {
                // \u83B7\u53D6\u89C6\u53E3\u5C3A\u5BF8
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // \u786E\u4FDD\u4F7F\u7528\u5F53\u524D\u6E38\u620F\u7684\u884C\u5217\u6570
                const currentRows = this.rows || 9;
                const currentCols = this.cols || 9;

                // \u52A8\u6001\u83B7\u53D6\u6392\u884C\u699C\u9762\u677F\u7684\u5B9E\u9645\u5360\u7528\u5BBD\u5EA6
                const leaderboardPanel = document.querySelector('.leaderboard-panel');
                let leaderboardWidth = 0;

                if (leaderboardPanel && window.getComputedStyle(leaderboardPanel).display !== 'none') {
                    // \u83B7\u53D6\u6392\u884C\u699C\u9762\u677F\u7684\u5B9E\u9645\u5BBD\u5EA6\uFF0C\u5305\u62EC\u8FB9\u8DDD\u548C\u8FB9\u6846
                    const rect = leaderboardPanel.getBoundingClientRect();
                    const computedStyle = window.getComputedStyle(leaderboardPanel);
                    const marginLeft = parseFloat(computedStyle.marginLeft) || 0;
                    const marginRight = parseFloat(computedStyle.marginRight) || 0;
                    leaderboardWidth = rect.width + marginLeft + marginRight;
                }

                // \u7CBE\u786E\u6D4B\u91CF\u5B9E\u9645\u56FA\u5B9A\u5143\u7D20\u7684\u9AD8\u5EA6
                const difficultySelector = document.querySelector('.difficulty-selector');
                const gameHeader = document.querySelector('.game-header');
                const mainContainer = document.querySelector('.main-container');

                let actualFixedHeight = 70; // \u9ED8\u8BA4\u503C
                if (difficultySelector && gameHeader) {
                    actualFixedHeight = difficultySelector.offsetHeight + gameHeader.offsetHeight;

                    // \u6DFB\u52A0\u5BB9\u5668\u7684\u5185\u8FB9\u8DDD
                    if (mainContainer) {
                        const containerStyle = window.getComputedStyle(mainContainer);
                        const paddingTop = parseFloat(containerStyle.paddingTop) || 0;
                        const paddingBottom = parseFloat(containerStyle.paddingBottom) || 0;
                        actualFixedHeight += paddingTop + paddingBottom;
                    }

                    // \u6DFB\u52A0\u5B89\u5168\u8FB9\u8DDD
                    actualFixedHeight += 30;
                }

                // \u8BA1\u7B97\u6E38\u620F\u533A\u57DF\u7684\u5B9E\u9645\u53EF\u7528\u7A7A\u95F4
                const gameContainer = document.querySelector('.game-container');
                let horizontalMargins = 40; // \u9ED8\u8BA4\u8FB9\u8DDD

                if (gameContainer) {
                    const containerStyle = window.getComputedStyle(gameContainer);
                    const marginLeft = parseFloat(containerStyle.marginLeft) || 0;
                    const marginRight = parseFloat(containerStyle.marginRight) || 0;
                    const paddingLeft = parseFloat(containerStyle.paddingLeft) || 0;
                    const paddingRight = parseFloat(containerStyle.paddingRight) || 0;
                    horizontalMargins = marginLeft + marginRight + paddingLeft + paddingRight + 20; // \u989D\u5916\u5B89\u5168\u8FB9\u8DDD
                }

                const availableWidth = viewportWidth - leaderboardWidth - horizontalMargins;
                const availableHeight = viewportHeight - actualFixedHeight;

                // \u8FB9\u754C\u68C0\u67E5 - \u786E\u4FDD\u53EF\u7528\u7A7A\u95F4\u5408\u7406
                if (availableWidth < 200 || availableHeight < 150) {
                    console.warn('\u53EF\u7528\u7A7A\u95F4\u8FC7\u5C0F\uFF0C\u4F7F\u7528\u6700\u5C0F\u5E03\u5C40');
                    return this.calculateCellSizeWithWidth(
                        Math.max(200, availableWidth),
                        Math.max(150, availableHeight),
                        currentRows,
                        currentCols
                    );
                }

                // \u68C0\u67E5\u4E13\u5BB6\u7EA7\u662F\u5426\u9700\u8981\u9690\u85CF\u6392\u884C\u699C
                if (this.currentDifficulty === 'expert' && leaderboardWidth > 0 && availableWidth < 800) {
                    this.autoHideLeaderboard();
                    // \u91CD\u65B0\u8BA1\u7B97\u53EF\u7528\u5BBD\u5EA6\uFF08\u4E0D\u5305\u62EC\u6392\u884C\u699C\uFF09
                    const newAvailableWidth = viewportWidth - horizontalMargins;
                    return this.calculateCellSizeWithWidth(newAvailableWidth, availableHeight, currentRows, currentCols);
                }

                return this.calculateCellSizeWithWidth(availableWidth, availableHeight, currentRows, currentCols);
            }

            // \u8F85\u52A9\u51FD\u6570\uFF1A\u6839\u636E\u53EF\u7528\u5BBD\u5EA6\u8BA1\u7B97\u683C\u5B50\u5927\u5C0F
            calculateCellSizeWithWidth(availableWidth, availableHeight, currentRows, currentCols) {
                // \u8FB9\u754C\u68C0\u67E5 - \u786E\u4FDD\u8F93\u5165\u53C2\u6570\u5408\u7406
                if (availableWidth <= 0 || availableHeight <= 0 || currentRows <= 0 || currentCols <= 0) {
                    console.warn('\u8BA1\u7B97\u683C\u5B50\u5927\u5C0F\u65F6\u53C2\u6570\u65E0\u6548:', { availableWidth, availableHeight, currentRows, currentCols });
                    return this.setDefaultCellSize();
                }

                // \u57FA\u4E8E\u53EF\u7528\u7A7A\u95F4\u8BA1\u7B97\u683C\u5B50\u5927\u5C0F
                const maxCellSizeByWidth = Math.floor(availableWidth / currentCols);
                const maxCellSizeByHeight = Math.floor(availableHeight / currentRows);

                // \u53D6\u8F83\u5C0F\u503C\u786E\u4FDD\u5B8C\u6574\u663E\u793A
                let optimalSize = Math.min(maxCellSizeByWidth, maxCellSizeByHeight);

                // \u8BBE\u7F6E\u5408\u7406\u7684\u5927\u5C0F\u8303\u56F4
                const minCellSize = 12;  // \u6700\u5C0F\u53EF\u7528\u5927\u5C0F
                const maxCellSize = 50;  // \u6700\u5927\u5408\u7406\u5927\u5C0F
                optimalSize = Math.max(minCellSize, Math.min(maxCellSize, optimalSize));

                // \u7CBE\u786E\u9A8C\u8BC1 - \u786E\u4FDD\u6E38\u620F\u677F\u4E0D\u4F1A\u8D85\u51FA\u53EF\u7528\u7A7A\u95F4
                const boardBorder = 2; // \u6E38\u620F\u677F\u8FB9\u6846
                const totalBoardWidth = optimalSize * currentCols + boardBorder;
                const totalBoardHeight = optimalSize * currentRows + boardBorder;

                // \u5982\u679C\u603B\u5C3A\u5BF8\u8D85\u51FA\u53EF\u7528\u7A7A\u95F4\uFF0C\u8FDB\u4E00\u6B65\u7F29\u5C0F
                if (totalBoardWidth > availableWidth || totalBoardHeight > availableHeight) {
                    const scaleX = availableWidth / totalBoardWidth;
                    const scaleY = availableHeight / totalBoardHeight;
                    const scale = Math.min(scaleX, scaleY) * 0.95; // \u75595%\u5B89\u5168\u8FB9\u8DDD
                    optimalSize = Math.floor(optimalSize * scale);
                    optimalSize = Math.max(minCellSize, optimalSize);
                }

                // \u6700\u7EC8\u8FB9\u754C\u68C0\u67E5
                if (optimalSize < minCellSize) {
                    console.warn('\u8BA1\u7B97\u51FA\u7684\u683C\u5B50\u5927\u5C0F\u8FC7\u5C0F\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u503C');
                    return this.setDefaultCellSize();
                }

                // \u6279\u91CF\u66F4\u65B0CSS\u53D8\u91CF\uFF08\u907F\u514D\u591A\u6B21\u91CD\u6392\uFF09
                this.updateCSSVariables(optimalSize);

                // \u8F93\u51FA\u8C03\u8BD5\u4FE1\u606F\uFF08\u4EC5\u5728\u5F00\u53D1\u6A21\u5F0F\u4E0B\uFF09
                if (console.log) {
                    console.log('\u5E03\u5C40\u8BA1\u7B97\u7ED3\u679C:');
                    console.log('  \u89C6\u53E3\u5C3A\u5BF8: ' + window.innerWidth + 'x' + window.innerHeight + 'px');
                    console.log('  \u53EF\u7528\u7A7A\u95F4: ' + availableWidth + 'x' + availableHeight + 'px');
                    console.log('  \u6E38\u620F\u5C3A\u5BF8: ' + currentCols + 'x' + currentRows);
                    console.log('  \u683C\u5B50\u5927\u5C0F: ' + optimalSize + 'px');
                    console.log('  \u6E38\u620F\u677F\u5C3A\u5BF8: ' + totalBoardWidth + 'x' + totalBoardHeight + 'px');
                }

                return optimalSize;
            }

            // \u8BBE\u7F6E\u9ED8\u8BA4\u683C\u5B50\u5927\u5C0F
            setDefaultCellSize() {
                const defaultSize = 25;
                this.updateCSSVariables(defaultSize);
                return defaultSize;
            }

            // \u6279\u91CF\u66F4\u65B0CSS\u53D8\u91CF\uFF0C\u907F\u514D\u591A\u6B21\u91CD\u6392
            updateCSSVariables(cellSize) {
                const root = document.documentElement;

                // \u8BA1\u7B97\u76F8\u5173\u5C3A\u5BF8
                const counterFontSize = Math.max(12, Math.floor(cellSize * 0.6));
                const smileySize = Math.max(24, Math.floor(cellSize * 1.0));

                // \u6279\u91CF\u8BBE\u7F6ECSS\u53D8\u91CF
                root.style.setProperty('--cell-size', cellSize + 'px');
                root.style.setProperty('--counter-font-size', counterFontSize + 'px');
                root.style.setProperty('--smiley-size', smileySize + 'px');

                // \u4F7F\u7528requestAnimationFrame\u4F18\u5316\u91CD\u6392\u65F6\u673A
                if (window.requestAnimationFrame) {
                    requestAnimationFrame(() => {
                        // \u89E6\u53D1\u4E00\u6B21\u91CD\u6392\u4EE5\u5E94\u7528\u65B0\u7684CSS\u53D8\u91CF
                        root.offsetHeight;
                    });
                } else {
                    // \u964D\u7EA7\u5904\u7406
                    root.offsetHeight;
                }
            }

            // \u81EA\u52A8\u9690\u85CF\u6392\u884C\u699C\uFF08\u5F53\u7A7A\u95F4\u4E0D\u8DB3\u65F6\uFF09
            autoHideLeaderboard() {
                const leaderboardPanel = document.querySelector('.leaderboard-panel');
                const leaderboardToggle = document.querySelector('.leaderboard-toggle');

                if (leaderboardPanel) {
                    leaderboardPanel.style.display = 'none';
                    leaderboardPanel.classList.add('auto-hidden');
                }

                if (leaderboardToggle) {
                    leaderboardToggle.style.display = 'inline-block';
                }
            }

            // \u751F\u6210\u5730\u96F7 - Fisher-Yates\u6D17\u724C\u7B97\u6CD5
            generateMines(firstClickRow, firstClickCol) {
                const positions = [];

                // \u521B\u5EFA\u6240\u6709\u53EF\u80FD\u7684\u4F4D\u7F6E\uFF08\u9664\u4E86\u9996\u6B21\u70B9\u51FB\u4F4D\u7F6E\u53CA\u5176\u5468\u56F4\uFF09
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        // \u8DF3\u8FC7\u9996\u6B21\u70B9\u51FB\u4F4D\u7F6E\u53CA\u5176\u5468\u56F48\u4E2A\u683C\u5B50
                        if (Math.abs(row - firstClickRow) <= 1 && Math.abs(col - firstClickCol) <= 1) {
                            continue;
                        }
                        positions.push([row, col]);
                    }
                }

                // Fisher-Yates\u6D17\u724C
                for (let i = positions.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [positions[i], positions[j]] = [positions[j], positions[i]];
                }

                // \u653E\u7F6E\u5730\u96F7
                for (let i = 0; i < this.mineCount && i < positions.length; i++) {
                    const [row, col] = positions[i];
                    this.board[row][col] = -1; // -1 \u8868\u793A\u5730\u96F7
                }

                // \u8BA1\u7B97\u6570\u5B57
                this.calculateNumbers();
            }

            // \u8BA1\u7B97\u6BCF\u4E2A\u683C\u5B50\u5468\u56F4\u7684\u5730\u96F7\u6570
            calculateNumbers() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (this.board[row][col] !== -1) {
                            let count = 0;
                            for (let dr = -1; dr <= 1; dr++) {
                                for (let dc = -1; dc <= 1; dc++) {
                                    const newRow = row + dr;
                                    const newCol = col + dc;
                                    if (this.isValidCell(newRow, newCol) && this.board[newRow][newCol] === -1) {
                                        count++;
                                    }
                                }
                            }
                            this.board[row][col] = count;
                        }
                    }
                }
            }

            // \u68C0\u67E5\u5750\u6807\u662F\u5426\u6709\u6548
            isValidCell(row, col) {
                return row >= 0 && row < this.rows && col >= 0 && col < this.cols;
            }

            // \u5DE6\u952E\u70B9\u51FB\u5904\u7406
            handleLeftClick(row, col, event) {
                event.preventDefault();

                if (this.gameState === 'won' || this.gameState === 'lost') return;
                if (this.flagged[row][col]) return;

                // \u9996\u6B21\u70B9\u51FB
                if (this.firstClick) {
                    this.generateMines(row, col);
                    this.firstClick = false;
                    this.gameState = 'playing';
                    this.startTimer();
                    document.getElementById('smiley-button').textContent = '\u{1F60A}';
                }

                this.revealCell(row, col);
                this.updateDisplay();
                this.checkGameState();
            }

            // \u53F3\u952E\u70B9\u51FB\u5904\u7406
            handleRightClick(row, col, event) {
                event.preventDefault();

                if (this.gameState === 'won' || this.gameState === 'lost') return;
                if (this.revealed[row][col]) return;

                this.flagged[row][col] = !this.flagged[row][col];
                this.flagCount += this.flagged[row][col] ? 1 : -1;

                this.updateDisplay();
            }

            // \u9F20\u6807\u6309\u4E0B\u5904\u7406\uFF08\u7528\u4E8E\u53CC\u952E\u5FEB\u901F\u6316\u6398\uFF09- \u589E\u5F3A\u7248\u672C
            handleMouseDown(row, col, event) {
                if (this.gameState === 'won' || this.gameState === 'lost') return;

                // \u963B\u6B62\u9ED8\u8BA4\u884C\u4E3A\uFF0C\u907F\u514D\u4E0E\u5176\u4ED6\u4E8B\u4EF6\u51B2\u7A81
                event.preventDefault();

                const currentTime = Date.now();

                if (event.button === 0) { // \u5DE6\u952E
                    this.mouseButtons.left = true;
                    this.mouseDownTimestamp.left = currentTime;
                } else if (event.button === 2) { // \u53F3\u952E
                    this.mouseButtons.right = true;
                    this.mouseDownTimestamp.right = currentTime;
                }

                // \u68C0\u67E5\u662F\u5426\u53CC\u952E\u6309\u4E0B\uFF08\u589E\u52A0\u65F6\u95F4\u6233\u9A8C\u8BC1\uFF09
                if (this.mouseButtons.left && this.mouseButtons.right) {
                    // \u9A8C\u8BC1\u4E24\u4E2A\u6309\u952E\u7684\u65F6\u95F4\u6233\u5DEE\u5F02\u4E0D\u8D85\u8FC7200ms
                    const timeDiff = Math.abs(this.mouseDownTimestamp.left - this.mouseDownTimestamp.right);
                    if (timeDiff <= 200) {
                        this.isQuickDigActive = true;
                        this.quickDigCell = { row, col, timestamp: currentTime };
                        this.highlightQuickDigArea(row, col, true);

                        // \u6DFB\u52A0\u89C6\u89C9\u53CD\u9988
                        this.addQuickDigFeedback(row, col);
                    }
                }
            }

            // \u9F20\u6807\u91CA\u653E\u5904\u7406 - \u589E\u5F3A\u7248\u672C
            handleMouseUp(row, col, event) {
                if (this.gameState === 'won' || this.gameState === 'lost') return;

                // \u963B\u6B62\u9ED8\u8BA4\u884C\u4E3A
                event.preventDefault();

                const currentTime = Date.now();
                const wasQuickDigActive = this.isQuickDigActive;

                // \u66F4\u65B0\u9F20\u6807\u6309\u94AE\u72B6\u6001
                if (event.button === 0) { // \u5DE6\u952E\u91CA\u653E
                    this.mouseButtons.left = false;
                    this.mouseDownTimestamp.left = 0;
                } else if (event.button === 2) { // \u53F3\u952E\u91CA\u653E
                    this.mouseButtons.right = false;
                    this.mouseDownTimestamp.right = 0;
                }

                // \u6267\u884C\u5FEB\u901F\u6316\u6398\uFF08\u589E\u5F3A\u9A8C\u8BC1\uFF09
                if (wasQuickDigActive && this.quickDigCell &&
                    this.quickDigCell.row === row && this.quickDigCell.col === col) {

                    // \u9A8C\u8BC1\u5FEB\u901F\u6316\u6398\u7684\u6709\u6548\u6027
                    const timeSinceStart = currentTime - this.quickDigCell.timestamp;
                    if (timeSinceStart <= 2000) { // 2\u79D2\u5185\u6709\u6548
                        this.performQuickDig(row, col);
                    }
                }

                // \u6E05\u7406\u5FEB\u901F\u6316\u6398\u72B6\u6001
                this.cleanupQuickDigState();
            }

            // \u9AD8\u4EAE\u5FEB\u901F\u6316\u6398\u533A\u57DF - \u589E\u5F3A\u7248\u672C
            highlightQuickDigArea(row, col, highlight) {
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        const newRow = row + dr;
                        const newCol = col + dc;
                        if (this.isValidCell(newRow, newCol) && !this.revealed[newRow][newCol]) {
                            const cellIndex = newRow * this.cols + newCol;
                            const cellElement = document.querySelectorAll('.cell')[cellIndex];
                            if (cellElement) {
                                if (highlight) {
                                    cellElement.classList.add('quick-dig-highlight');
                                } else {
                                    cellElement.classList.remove('quick-dig-highlight');
                                }
                            }
                        }
                    }
                }
            }

            // \u6DFB\u52A0\u5FEB\u901F\u6316\u6398\u89C6\u89C9\u53CD\u9988
            addQuickDigFeedback(row, col) {
                const cellIndex = row * this.cols + col;
                const cellElement = document.querySelectorAll('.cell')[cellIndex];
                if (cellElement) {
                    cellElement.classList.add('quick-dig-active');
                    // \u77ED\u6682\u7684\u89C6\u89C9\u53CD\u9988
                    setTimeout(() => {
                        if (cellElement) {
                            cellElement.classList.remove('quick-dig-active');
                        }
                    }, 150);
                }
            }

            // \u6E05\u7406\u5FEB\u901F\u6316\u6398\u72B6\u6001
            cleanupQuickDigState() {
                if (this.quickDigCell) {
                    this.highlightQuickDigArea(this.quickDigCell.row, this.quickDigCell.col, false);
                    this.quickDigCell = null;
                }
                this.isQuickDigActive = false;

                // \u6E05\u7406\u6240\u6709\u53EF\u80FD\u6B8B\u7559\u7684\u9AD8\u4EAE\u72B6\u6001
                document.querySelectorAll('.cell').forEach(cell => {
                    cell.classList.remove('quick-dig-highlight', 'quick-dig-active');
                });
            }

            // \u5168\u5C40\u72B6\u6001\u91CD\u7F6E\uFF08\u7528\u4E8E\u5904\u7406\u610F\u5916\u60C5\u51B5\uFF09
            resetMouseState() {
                this.mouseButtons.left = false;
                this.mouseButtons.right = false;
                this.mouseDownTimestamp.left = 0;
                this.mouseDownTimestamp.right = 0;
                this.cleanupQuickDigState();
            }

            // \u6267\u884C\u5FEB\u901F\u6316\u6398
            performQuickDig(row, col) {
                // \u53EA\u6709\u5DF2\u63ED\u5F00\u7684\u6570\u5B57\u683C\u5B50\u624D\u80FD\u8FDB\u884C\u5FEB\u901F\u6316\u6398
                if (!this.revealed[row][col] || this.board[row][col] <= 0) {
                    return;
                }

                const targetNumber = this.board[row][col];
                let flaggedCount = 0;

                // \u8BA1\u7B97\u5468\u56F4\u6807\u8BB0\u7684\u5730\u96F7\u6570
                for (let dr = -1; dr <= 1; dr++) {
                    for (let dc = -1; dc <= 1; dc++) {
                        const newRow = row + dr;
                        const newCol = col + dc;
                        if (this.isValidCell(newRow, newCol) && this.flagged[newRow][newCol]) {
                            flaggedCount++;
                        }
                    }
                }

                // \u53EA\u6709\u5F53\u6807\u8BB0\u6570\u7B49\u4E8E\u76EE\u6807\u6570\u5B57\u65F6\u624D\u80FD\u5FEB\u901F\u6316\u6398
                if (flaggedCount === targetNumber) {
                    for (let dr = -1; dr <= 1; dr++) {
                        for (let dc = -1; dc <= 1; dc++) {
                            const newRow = row + dr;
                            const newCol = col + dc;
                            if (this.isValidCell(newRow, newCol) &&
                                !this.revealed[newRow][newCol] &&
                                !this.flagged[newRow][newCol]) {
                                this.revealCell(newRow, newCol);
                            }
                        }
                    }
                    this.updateDisplay();
                    this.checkGameState();
                }
            }

            // \u63ED\u5F00\u683C\u5B50
            revealCell(row, col) {
                if (!this.isValidCell(row, col) || this.revealed[row][col] || this.flagged[row][col]) {
                    return;
                }

                this.revealed[row][col] = true;

                // \u5982\u679C\u662F\u5730\u96F7\uFF0C\u6E38\u620F\u7ED3\u675F
                if (this.board[row][col] === -1) {
                    this.gameState = 'lost';
                    this.revealAllMines();
                    return;
                }

                // \u5982\u679C\u662F\u7A7A\u683C\u5B50\uFF08\u6570\u5B57\u4E3A0\uFF09\uFF0C\u81EA\u52A8\u5C55\u5F00\u5468\u56F4
                if (this.board[row][col] === 0) {
                    for (let dr = -1; dr <= 1; dr++) {
                        for (let dc = -1; dc <= 1; dc++) {
                            this.revealCell(row + dr, col + dc);
                        }
                    }
                }
            }

            // \u63ED\u5F00\u6240\u6709\u5730\u96F7\uFF08\u6E38\u620F\u5931\u8D25\u65F6\uFF09
            revealAllMines() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (this.board[row][col] === -1) {
                            this.revealed[row][col] = true;
                        }
                    }
                }
            }

            // \u68C0\u67E5\u6E38\u620F\u72B6\u6001
            checkGameState() {
                if (this.gameState === 'lost') {
                    document.getElementById('smiley-button').textContent = '\u{1F635}';
                    this.stopTimer();

                    // \u663E\u793A\u5931\u8D25\u63D0\u793A
                    setTimeout(() => {
                        this.showGameModal('\u{1F4A3}', '\u6E38\u620F\u5931\u8D25\uFF01', '\u8E29\u5230\u5730\u96F7\u4E86\uFF01\\n\u70B9\u51FB\u7B11\u8138\u6216\u65B0\u6E38\u620F\u6309\u94AE\u91CD\u65B0\u5F00\u59CB\u3002');
                    }, 500);
                    return;
                }

                // \u68C0\u67E5\u662F\u5426\u80DC\u5229
                let unrevealedCount = 0;
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (!this.revealed[row][col] && this.board[row][col] !== -1) {
                            unrevealedCount++;
                        }
                    }
                }

                if (unrevealedCount === 0) {
                    this.gameState = 'won';
                    document.getElementById('smiley-button').textContent = '\u{1F60E}';
                    this.stopTimer();

                    // \u81EA\u52A8\u6807\u8BB0\u6240\u6709\u5730\u96F7
                    for (let row = 0; row < this.rows; row++) {
                        for (let col = 0; col < this.cols; col++) {
                            if (this.board[row][col] === -1 && !this.flagged[row][col]) {
                                this.flagged[row][col] = true;
                                this.flagCount++;
                            }
                        }
                    }

                    // \u663E\u793A\u80DC\u5229\u63D0\u793A
                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                    setTimeout(() => {
                        const message = '\u7528\u65F6\uFF1A' + elapsed + '\u79D2\\n\u96BE\u5EA6\uFF1A' + this.getDifficultyName() + '\\n\\n\u592A\u68D2\u4E86\uFF01\u4F60\u6210\u529F\u627E\u51FA\u4E86\u6240\u6709\u5730\u96F7\uFF01';
                        this.showGameModal('\u{1F389}', '\u606D\u559C\u80DC\u5229\uFF01', message, elapsed, this.currentDifficulty);
                    }, 500);
                }
            }

            // \u83B7\u53D6\u96BE\u5EA6\u540D\u79F0
            getDifficultyName() {
                const names = {
                    'beginner': '\u521D\u7EA7',
                    'intermediate': '\u4E2D\u7EA7',
                    'expert': '\u4E13\u5BB6'
                };
                return names[this.currentDifficulty] || '\u81EA\u5B9A\u4E49';
            }

            // \u663E\u793A\u6E38\u620F\u7ED3\u675F\u63D0\u793A\u6846
            showGameModal(icon, title, message, time = null, difficulty = null) {
                const modal = document.getElementById('game-modal');
                const uploadBtn = document.getElementById('upload-score-btn');

                document.getElementById('modal-icon').textContent = icon;
                document.getElementById('modal-title').textContent = title;
                document.getElementById('modal-message').innerHTML = message.replace(/\\n/g, '<br>');

                // \u5982\u679C\u662F\u80DC\u5229\u4E14\u6709\u65F6\u95F4\u548C\u96BE\u5EA6\uFF0C\u663E\u793A\u4E0A\u4F20\u6309\u94AE
                if (time !== null && difficulty !== null) {
                    uploadBtn.style.display = 'inline-block';
                    // \u4FDD\u5B58\u6210\u7EE9\u4FE1\u606F\u4F9B\u4E0A\u4F20\u4F7F\u7528
                    window.currentScore = { time, difficulty };
                } else {
                    uploadBtn.style.display = 'none';
                    window.currentScore = null;
                }

                modal.style.display = 'flex';
                setTimeout(() => {
                    modal.classList.add('show');
                }, 10);
            }

            // \u5F00\u59CB\u8BA1\u65F6\u5668
            startTimer() {
                this.startTime = Date.now();
                this.timer = setInterval(() => {
                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                    const displayTime = Math.min(elapsed, 999);
                    document.getElementById('timer').textContent = displayTime.toString().padStart(3, '0');
                }, 1000);
            }

            // \u505C\u6B62\u8BA1\u65F6\u5668
            stopTimer() {
                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
            }

            // \u66F4\u65B0\u663E\u793A - \u4F18\u5316\u7248\u672C\uFF0C\u652F\u6301\u589E\u91CF\u66F4\u65B0
            updateDisplay(forceFullUpdate = false) {
                // \u66F4\u65B0\u5730\u96F7\u8BA1\u6570\u5668
                const remainingMines = this.mineCount - this.flagCount;
                document.getElementById('mine-counter').textContent =
                    Math.max(-99, Math.min(999, remainingMines)).toString().padStart(3, '0');

                // \u9632\u6B62\u91CD\u590D\u7684\u66F4\u65B0\u8BF7\u6C42
                if (this.updatePending && !forceFullUpdate) {
                    return;
                }

                this.updatePending = true;

                // \u4F7F\u7528requestAnimationFrame\u4F18\u5316\u66F4\u65B0\u65F6\u673A
                requestAnimationFrame(() => {
                    this.performDisplayUpdate(forceFullUpdate);
                    this.updatePending = false;
                });
            }

            // \u6267\u884C\u5B9E\u9645\u7684\u663E\u793A\u66F4\u65B0
            performDisplayUpdate(forceFullUpdate = false) {
                const cells = document.querySelectorAll('.cell');

                if (forceFullUpdate || this.previousCellStates.length === 0) {
                    // \u5168\u91CF\u66F4\u65B0\uFF08\u9996\u6B21\u6216\u5F3A\u5236\u66F4\u65B0\uFF09
                    this.performFullUpdate(cells);
                } else {
                    // \u589E\u91CF\u66F4\u65B0\uFF08\u53EA\u66F4\u65B0\u53D8\u5316\u7684\u683C\u5B50\uFF09
                    this.performIncrementalUpdate(cells);
                }

                // \u66F4\u65B0\u72B6\u6001\u8DDF\u8E2A
                this.updateStateTracking();
            }

            // \u6267\u884C\u5168\u91CF\u66F4\u65B0
            performFullUpdate(cells) {
                cells.forEach((cell, index) => {
                    const row = Math.floor(index / this.cols);
                    const col = index % this.cols;
                    this.updateSingleCell(cell, row, col);
                });
            }

            // \u6267\u884C\u589E\u91CF\u66F4\u65B0
            performIncrementalUpdate(cells) {
                // \u68C0\u6D4B\u53D8\u5316\u7684\u683C\u5B50
                this.detectChangedCells();

                // \u53EA\u66F4\u65B0\u53D8\u5316\u7684\u683C\u5B50
                this.changedCells.forEach(cellIndex => {
                    const row = Math.floor(cellIndex / this.cols);
                    const col = cellIndex % this.cols;
                    const cell = cells[cellIndex];
                    if (cell) {
                        this.updateSingleCell(cell, row, col);
                    }
                });

                // \u6E05\u9664\u53D8\u5316\u8BB0\u5F55
                this.changedCells.clear();
            }

            // \u68C0\u6D4B\u53D1\u751F\u53D8\u5316\u7684\u683C\u5B50
            detectChangedCells() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        const currentState = this.getCellState(row, col);
                        const previousState = this.previousCellStates[row][col];

                        if (currentState !== previousState) {
                            const cellIndex = row * this.cols + col;
                            this.changedCells.add(cellIndex);
                        }
                    }
                }
            }

            // \u83B7\u53D6\u683C\u5B50\u7684\u5F53\u524D\u72B6\u6001\uFF08\u7528\u4E8E\u6BD4\u8F83\uFF09
            getCellState(row, col) {
                if (this.flagged[row][col]) {
                    return 'flagged';
                } else if (this.revealed[row][col]) {
                    if (this.board[row][col] === -1) {
                        return 'mine';
                    } else if (this.board[row][col] > 0) {
                        return 'number-' + this.board[row][col];
                    } else {
                        return 'revealed-empty';
                    }
                } else {
                    return 'hidden';
                }
            }

            // \u66F4\u65B0\u5355\u4E2A\u683C\u5B50\u7684\u663E\u793A
            updateSingleCell(cell, row, col) {
                // \u6E05\u9664\u6240\u6709\u72B6\u6001\u7C7B
                cell.className = 'cell';
                cell.textContent = '';

                if (this.flagged[row][col]) {
                    cell.classList.add('flagged');
                } else if (this.revealed[row][col]) {
                    cell.classList.add('revealed');

                    if (this.board[row][col] === -1) {
                        cell.classList.add('mine');
                        cell.textContent = '\u{1F4A3}';
                    } else if (this.board[row][col] > 0) {
                        cell.classList.add('number-' + this.board[row][col]);
                        cell.textContent = this.board[row][col];
                    }
                }
            }

            // \u66F4\u65B0\u72B6\u6001\u8DDF\u8E2A
            updateStateTracking() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        this.previousCellStates[row][col] = this.getCellState(row, col);
                    }
                }
            }
        }

        // \u6392\u884C\u699C\u7BA1\u7406\u5668 - \u589E\u5F3A\u7248\u672C
        class LeaderboardManager {
            constructor(game) {
                this.game = game;
                this.currentDifficulty = 'beginner';

                // \u589E\u5F3A\u7684\u7F13\u5B58\u7CFB\u7EDF
                this.cache = {}; // \u7F13\u5B58\u6570\u636E
                this.cacheTimestamps = {}; // \u7F13\u5B58\u65F6\u95F4\u6233
                this.cacheVersions = {}; // \u7F13\u5B58\u7248\u672C\u63A7\u5236
                this.cacheExpiry = 5 * 60 * 1000; // 5\u5206\u949F\u8FC7\u671F\u65F6\u95F4

                // \u8BF7\u6C42\u72B6\u6001\u7BA1\u7406
                this.isLoading = false;
                this.loadingPromises = {}; // \u9632\u6B62\u91CD\u590D\u8BF7\u6C42
                this.retryAttempts = {}; // \u91CD\u8BD5\u8BA1\u6570
                this.maxRetries = 3; // \u6700\u5927\u91CD\u8BD5\u6B21\u6570

                // \u7F51\u7EDC\u72B6\u6001\u68C0\u6D4B
                this.isOnline = navigator.onLine;
                this.networkRetryDelay = 1000; // \u7F51\u7EDC\u91CD\u8BD5\u5EF6\u8FDF\uFF08\u6BEB\u79D2\uFF09

                // \u79BB\u7EBF\u6A21\u5F0F\u652F\u6301
                this.offlineData = {}; // \u79BB\u7EBF\u6570\u636E\u5B58\u50A8
                this.pendingUploads = []; // \u5F85\u4E0A\u4F20\u961F\u5217

                // \u521D\u59CB\u5316\u7F51\u7EDC\u72B6\u6001\u76D1\u542C
                this.initNetworkListeners();
            }

            // \u521D\u59CB\u5316\u7F51\u7EDC\u72B6\u6001\u76D1\u542C\u5668
            initNetworkListeners() {
                window.addEventListener('online', () => {
                    this.isOnline = true;
                    console.log('\u7F51\u7EDC\u8FDE\u63A5\u5DF2\u6062\u590D');
                    this.processPendingUploads();
                });

                window.addEventListener('offline', () => {
                    this.isOnline = false;
                    console.log('\u7F51\u7EDC\u8FDE\u63A5\u5DF2\u65AD\u5F00\uFF0C\u8FDB\u5165\u79BB\u7EBF\u6A21\u5F0F');
                });
            }

            // \u68C0\u67E5\u7F13\u5B58\u662F\u5426\u6709\u6548
            isCacheValid(difficulty) {
                if (!this.cache[difficulty] || !this.cacheTimestamps[difficulty]) {
                    return false;
                }

                const now = Date.now();
                const cacheAge = now - this.cacheTimestamps[difficulty];
                return cacheAge < this.cacheExpiry;
            }

            // \u66F4\u65B0\u7F13\u5B58
            updateCache(difficulty, data, version = null) {
                this.cache[difficulty] = data;
                this.cacheTimestamps[difficulty] = Date.now();
                this.cacheVersions[difficulty] = version || Date.now();

                // \u540C\u65F6\u66F4\u65B0\u79BB\u7EBF\u6570\u636E
                this.offlineData[difficulty] = {
                    data: data,
                    timestamp: Date.now(),
                    version: this.cacheVersions[difficulty]
                };
            }

            // \u83B7\u53D6\u7F13\u5B58\u6570\u636E\uFF08\u5305\u62EC\u79BB\u7EBF\u6570\u636E\uFF09
            getCachedData(difficulty) {
                // \u4F18\u5148\u4F7F\u7528\u5728\u7EBF\u7F13\u5B58
                if (this.isCacheValid(difficulty)) {
                    return this.cache[difficulty];
                }

                // \u5982\u679C\u79BB\u7EBF\uFF0C\u4F7F\u7528\u79BB\u7EBF\u6570\u636E
                if (!this.isOnline && this.offlineData[difficulty]) {
                    return this.offlineData[difficulty].data;
                }

                return null;
            }

            // \u6307\u6570\u9000\u907F\u91CD\u8BD5\u673A\u5236
            async retryWithBackoff(fn, difficulty, attempt = 0) {
                try {
                    return await fn();
                } catch (error) {
                    if (attempt >= this.maxRetries) {
                        throw error;
                    }

                    const delay = Math.min(1000 * Math.pow(2, attempt), 10000); // \u6700\u592710\u79D2
                    console.log('\u91CD\u8BD5\u7B2C ' + (attempt + 1) + ' \u6B21\uFF0C\u5EF6\u8FDF ' + delay + 'ms');

                    await new Promise(resolve => setTimeout(resolve, delay));
                    return this.retryWithBackoff(fn, difficulty, attempt + 1);
                }
            }

            async loadLeaderboard(difficulty) {
                // \u9632\u6B62\u91CD\u590D\u8BF7\u6C42
                if (this.loadingPromises[difficulty]) {
                    return this.loadingPromises[difficulty];
                }

                // \u68C0\u67E5\u7F13\u5B58
                const cachedData = this.getCachedData(difficulty);
                if (cachedData) {
                    this.updateDisplay(cachedData);
                    return;
                }

                // \u5982\u679C\u79BB\u7EBF\u4E14\u6CA1\u6709\u7F13\u5B58\u6570\u636E
                if (!this.isOnline) {
                    this.showError('\u7F51\u7EDC\u8FDE\u63A5\u4E0D\u53EF\u7528\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8BBE\u7F6E');
                    return;
                }

                this.isLoading = true;
                this.showLoading();

                // \u521B\u5EFA\u52A0\u8F7DPromise
                this.loadingPromises[difficulty] = this.performLoadLeaderboard(difficulty);

                try {
                    await this.loadingPromises[difficulty];
                } finally {
                    delete this.loadingPromises[difficulty];
                    this.hideLoading();
                    this.isLoading = false;
                }
            }

            // \u6267\u884C\u5B9E\u9645\u7684\u6392\u884C\u699C\u52A0\u8F7D
            async performLoadLeaderboard(difficulty) {
                const loadFunction = async () => {
                    const response = await fetch('/api/leaderboard/' + difficulty, {
                        method: 'GET',
                        headers: {
                            'Cache-Control': 'no-cache',
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                    }

                    const result = await response.json();

                    if (!result.success) {
                        throw new Error(result.error || '\u670D\u52A1\u5668\u8FD4\u56DE\u9519\u8BEF');
                    }

                    return result;
                };

                try {
                    const result = await this.retryWithBackoff(loadFunction, difficulty);

                    // \u66F4\u65B0\u7F13\u5B58\u548C\u663E\u793A
                    this.updateCache(difficulty, result.data, result.version);
                    this.updateDisplay(result.data);

                    // \u91CD\u7F6E\u91CD\u8BD5\u8BA1\u6570
                    this.retryAttempts[difficulty] = 0;

                } catch (error) {
                    console.error('\u52A0\u8F7D\u6392\u884C\u699C\u5931\u8D25:', error);
                    this.handleLoadError(difficulty, error);
                }
            }

            // \u5904\u7406\u52A0\u8F7D\u9519\u8BEF
            handleLoadError(difficulty, error) {
                let errorMessage = '\u52A0\u8F7D\u6392\u884C\u699C\u5931\u8D25';

                if (error.message.includes('HTTP 404')) {
                    errorMessage = '\u6392\u884C\u699C\u6570\u636E\u4E0D\u5B58\u5728';
                } else if (error.message.includes('HTTP 500')) {
                    errorMessage = '\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMessage = '\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8BBE\u7F6E';
                } else if (error.message.includes('timeout')) {
                    errorMessage = '\u8BF7\u6C42\u8D85\u65F6\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5';
                } else {
                    errorMessage = '\u52A0\u8F7D\u5931\u8D25: ' + error.message;
                }

                // \u5C1D\u8BD5\u663E\u793A\u79BB\u7EBF\u6570\u636E
                const offlineData = this.offlineData[difficulty];
                if (offlineData) {
                    this.updateDisplay(offlineData.data);
                    this.showOfflineNotice();
                } else {
                    this.showError(errorMessage);
                }
            }

            async uploadScore(username, time, difficulty) {
                const scoreData = { username, time, difficulty, timestamp: Date.now() };

                // \u5982\u679C\u79BB\u7EBF\uFF0C\u6DFB\u52A0\u5230\u5F85\u4E0A\u4F20\u961F\u5217
                if (!this.isOnline) {
                    this.pendingUploads.push(scoreData);
                    return {
                        success: false,
                        error: '\u7F51\u7EDC\u8FDE\u63A5\u4E0D\u53EF\u7528\uFF0C\u6210\u7EE9\u5DF2\u4FDD\u5B58\uFF0C\u5C06\u5728\u7F51\u7EDC\u6062\u590D\u540E\u81EA\u52A8\u4E0A\u4F20'
                    };
                }

                const uploadFunction = async () => {
                    const response = await fetch('/api/leaderboard/' + difficulty, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Cache-Control': 'no-cache'
                        },
                        body: JSON.stringify({
                            username,
                            time,
                            clientTimestamp: Date.now(),
                            version: this.cacheVersions[difficulty] || 0
                        })
                    });

                    if (!response.ok) {
                        throw new Error('HTTP ' + response.status + ': ' + response.statusText);
                    }

                    const result = await response.json();

                    if (!result.success) {
                        throw new Error(result.error || '\u4E0A\u4F20\u5931\u8D25');
                    }

                    return result;
                };

                try {
                    const result = await this.retryWithBackoff(uploadFunction, difficulty);

                    // \u66F4\u65B0\u7F13\u5B58\u548C\u663E\u793A
                    this.updateCache(difficulty, result.data, result.version);
                    if (this.currentDifficulty === difficulty) {
                        this.updateDisplay(result.data, username);
                    }

                    return { success: true };

                } catch (error) {
                    console.error('\u4E0A\u4F20\u6210\u7EE9\u5931\u8D25:', error);

                    // \u6839\u636E\u9519\u8BEF\u7C7B\u578B\u8FD4\u56DE\u4E0D\u540C\u7684\u9519\u8BEF\u4FE1\u606F
                    let errorMessage = '\u4E0A\u4F20\u5931\u8D25';

                    if (error.message.includes('HTTP 409')) {
                        errorMessage = '\u6570\u636E\u51B2\u7A81\uFF0C\u8BF7\u5237\u65B0\u6392\u884C\u699C\u540E\u91CD\u8BD5';
                    } else if (error.message.includes('HTTP 400')) {
                        errorMessage = '\u6570\u636E\u683C\u5F0F\u9519\u8BEF\uFF0C\u8BF7\u68C0\u67E5\u7528\u6237\u540D\u548C\u6210\u7EE9';
                    } else if (error.message.includes('HTTP 429')) {
                        errorMessage = '\u8BF7\u6C42\u8FC7\u4E8E\u9891\u7E41\uFF0C\u8BF7\u7A0D\u540E\u91CD\u8BD5';
                    } else if (error.message.includes('Failed to fetch')) {
                        // \u7F51\u7EDC\u9519\u8BEF\uFF0C\u6DFB\u52A0\u5230\u5F85\u4E0A\u4F20\u961F\u5217
                        this.pendingUploads.push(scoreData);
                        errorMessage = '\u7F51\u7EDC\u8FDE\u63A5\u5931\u8D25\uFF0C\u6210\u7EE9\u5DF2\u4FDD\u5B58\uFF0C\u5C06\u5728\u7F51\u7EDC\u6062\u590D\u540E\u81EA\u52A8\u4E0A\u4F20';
                    } else {
                        errorMessage = '\u4E0A\u4F20\u5931\u8D25: ' + error.message;
                    }

                    return { success: false, error: errorMessage };
                }
            }

            switchDifficulty(difficulty) {
                this.currentDifficulty = difficulty;

                // \u66F4\u65B0\u6807\u7B7E\u9875\u72B6\u6001
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.difficulty === difficulty) {
                        btn.classList.add('active');
                    }
                });

                // \u52A0\u8F7D\u5BF9\u5E94\u96BE\u5EA6\u7684\u6392\u884C\u699C
                this.loadLeaderboard(difficulty);
            }

            updateDisplay(data, highlightUsername = null) {
                const listElement = document.getElementById('leaderboard-list');
                const emptyElement = document.getElementById('leaderboard-empty');

                if (data.length === 0) {
                    listElement.style.display = 'none';
                    emptyElement.style.display = 'block';
                    return;
                }

                listElement.style.display = 'block';
                emptyElement.style.display = 'none';

                listElement.innerHTML = data.map((record, index) => {
                    const rank = index + 1;
                    const rankClass = rank === 1 ? 'gold' : rank === 2 ? 'silver' : rank === 3 ? 'bronze' : '';
                    const highlightClass = highlightUsername && record.username === highlightUsername ? 'highlight' : '';

                    return '<div class="leaderboard-item ' + highlightClass + '"><div class="leaderboard-rank ' + rankClass + '">' + rank + '</div><div class="leaderboard-username">' + this.escapeHtml(record.username) + '</div><div class="leaderboard-time">' + record.time + 's</div></div>';
                }).join('');
            }

            showLoading() {
                document.getElementById('leaderboard-loading').style.display = 'flex';
                document.getElementById('leaderboard-list').style.display = 'none';
                document.getElementById('leaderboard-empty').style.display = 'none';
            }

            hideLoading() {
                document.getElementById('leaderboard-loading').style.display = 'none';
            }

            // \u5904\u7406\u5F85\u4E0A\u4F20\u961F\u5217
            async processPendingUploads() {
                if (this.pendingUploads.length === 0) return;

                console.log('\u5F00\u59CB\u5904\u7406 ' + this.pendingUploads.length + ' \u4E2A\u5F85\u4E0A\u4F20\u6210\u7EE9');

                const uploads = [...this.pendingUploads];
                this.pendingUploads = [];

                for (const scoreData of uploads) {
                    try {
                        const result = await this.uploadScore(
                            scoreData.username,
                            scoreData.time,
                            scoreData.difficulty
                        );

                        if (result.success) {
                            console.log('\u6210\u529F\u4E0A\u4F20\u6210\u7EE9: ' + scoreData.username + ' - ' + scoreData.time + 's');
                        } else {
                            // \u5982\u679C\u4E0A\u4F20\u5931\u8D25\uFF0C\u91CD\u65B0\u52A0\u5165\u961F\u5217
                            this.pendingUploads.push(scoreData);
                        }
                    } catch (error) {
                        console.error('\u5904\u7406\u5F85\u4E0A\u4F20\u6210\u7EE9\u5931\u8D25:', error);
                        // \u91CD\u65B0\u52A0\u5165\u961F\u5217
                        this.pendingUploads.push(scoreData);
                    }
                }
            }

            // \u663E\u793A\u79BB\u7EBF\u63D0\u793A
            showOfflineNotice() {
                const listElement = document.getElementById('leaderboard-list');
                listElement.innerHTML =
                    '<div style="text-align: center; color: #666; padding: 20px; border: 1px dashed #ccc; margin: 10px 0;">' +
                        '<div style="font-size: 24px; margin-bottom: 10px;">\u{1F4F1}</div>' +
                        '<div style="font-weight: bold; margin-bottom: 5px;">\u79BB\u7EBF\u6A21\u5F0F</div>' +
                        '<div style="font-size: 12px;">\u663E\u793A\u7684\u662F\u7F13\u5B58\u6570\u636E\uFF0C\u7F51\u7EDC\u6062\u590D\u540E\u5C06\u81EA\u52A8\u66F4\u65B0</div>' +
                    '</div>';
                listElement.style.display = 'block';
            }

            showError(message) {
                const listElement = document.getElementById('leaderboard-list');
                listElement.innerHTML =
                    '<div style="text-align: center; color: #d00; padding: 20px;">' +
                        '<div style="font-size: 24px; margin-bottom: 10px;">\u26A0\uFE0F</div>' +
                        '<div style="font-weight: bold; margin-bottom: 5px;">\u52A0\u8F7D\u5931\u8D25</div>' +
                        '<div style="font-size: 12px;">' + message + '</div>' +
                        '<button onclick="window.leaderboardManager.loadLeaderboard('' + this.currentDifficulty + '')" ' +
                                'style="margin-top: 10px; padding: 5px 10px; background: #c0c0c0; border: 1px outset #c0c0c0; cursor: pointer;">' +
                            '\u91CD\u8BD5' +
                        '</button>' +
                    '</div>';
                listElement.style.display = 'block';
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // \u6E05\u9664\u6307\u5B9A\u96BE\u5EA6\u7684\u7F13\u5B58 - \u589E\u5F3A\u7248\u672C
            clearCache(difficulty) {
                if (difficulty) {
                    delete this.cache[difficulty];
                    delete this.cacheTimestamps[difficulty];
                    delete this.cacheVersions[difficulty];
                    delete this.offlineData[difficulty];
                } else {
                    this.cache = {};
                    this.cacheTimestamps = {};
                    this.cacheVersions = {};
                    this.offlineData = {};
                }
            }

            // \u83B7\u53D6\u7F13\u5B58\u72B6\u6001\u4FE1\u606F
            getCacheStatus() {
                const status = {};
                for (const difficulty in this.cache) {
                    const isValid = this.isCacheValid(difficulty);
                    const age = Date.now() - (this.cacheTimestamps[difficulty] || 0);
                    status[difficulty] = {
                        valid: isValid,
                        age: Math.floor(age / 1000), // \u79D2
                        version: this.cacheVersions[difficulty],
                        hasOfflineData: !!this.offlineData[difficulty]
                    };
                }
                return status;
            }

            // \u5F3A\u5236\u5237\u65B0\u6392\u884C\u699C
            async forceRefresh(difficulty) {
                this.clearCache(difficulty);
                await this.loadLeaderboard(difficulty);
            }

            // \u83B7\u53D6\u7F51\u7EDC\u72B6\u6001\u548C\u7EDF\u8BA1\u4FE1\u606F
            getNetworkStatus() {
                return {
                    isOnline: this.isOnline,
                    pendingUploads: this.pendingUploads.length,
                    retryAttempts: { ...this.retryAttempts },
                    cacheStatus: this.getCacheStatus()
                };
            }
        }

        // \u5168\u5C40\u6E38\u620F\u5B9E\u4F8B
        let game = new MinesweeperGame();

        // \u5168\u5C40\u6392\u884C\u699C\u7BA1\u7406\u5668
        window.leaderboardManager = new LeaderboardManager(game);

        // \u5168\u5C40\u4E8B\u4EF6\u76D1\u542C\u5668 - \u5904\u7406\u610F\u5916\u60C5\u51B5\u548C\u72B6\u6001\u91CD\u7F6E
        document.addEventListener('mouseup', (e) => {
            // \u53EA\u5728\u9F20\u6807\u79BB\u5F00\u6E38\u620F\u533A\u57DF\u65F6\u91CD\u7F6E\u72B6\u6001
            if (!e.target.closest('#board-grid')) {
                game.resetMouseState();
            }
        });

        // \u5904\u7406\u9F20\u6807\u79BB\u5F00\u6E38\u620F\u533A\u57DF\u7684\u60C5\u51B5
        document.addEventListener('mouseleave', () => {
            game.resetMouseState();
        });

        // \u5904\u7406\u7A97\u53E3\u5931\u53BB\u7126\u70B9\u7684\u60C5\u51B5
        window.addEventListener('blur', () => {
            game.resetMouseState();
        });

        // \u9632\u6B62\u53F3\u952E\u83DC\u5355
        document.addEventListener('contextmenu', (e) => {
            if (e.target.classList.contains('cell')) {
                e.preventDefault();
            }
        });

        // \u7A97\u53E3\u5927\u5C0F\u53D8\u5316\u65F6\u91CD\u65B0\u8BA1\u7B97\u5E03\u5C40
        window.addEventListener('resize', () => {
            if (game) {
                game.calculateOptimalCellSize();
            }
        });

        // \u70B9\u51FB\u63D0\u793A\u6846\u80CC\u666F\u5173\u95ED
        document.getElementById('game-modal').addEventListener('click', (e) => {
            if (e.target.id === 'game-modal') {
                closeModal();
            }
        });

        // \u70B9\u51FB\u5E2E\u52A9\u63D0\u793A\u6846\u80CC\u666F\u5173\u95ED
        document.getElementById('help-modal').addEventListener('click', (e) => {
            if (e.target.id === 'help-modal') {
                closeHelpModal();
            }
        });
        
        // \u5DE5\u5177\u51FD\u6570

        // \u9632\u6296\u51FD\u6570 - \u4F18\u5316\u7A97\u53E3\u5927\u5C0F\u53D8\u5316\u4E8B\u4EF6\u5904\u7406
        function debounce(func, wait, immediate = false) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    timeout = null;
                    if (!immediate) func.apply(this, args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(this, args);
            };
        }

        // \u8282\u6D41\u51FD\u6570 - \u9650\u5236\u51FD\u6570\u6267\u884C\u9891\u7387
        function throttle(func, limit) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }

        // \u7A97\u53E3\u5927\u5C0F\u53D8\u5316\u5904\u7406\u51FD\u6570
        function handleWindowResize() {
            if (game && game.calculateOptimalCellSize) {
                console.log('\u7A97\u53E3\u5927\u5C0F\u53D8\u5316\uFF0C\u91CD\u65B0\u8BA1\u7B97\u5E03\u5C40');
                game.calculateOptimalCellSize();
            }
        }

        // \u521B\u5EFA\u9632\u6296\u7248\u672C\u7684resize\u5904\u7406\u51FD\u6570
        const debouncedResize = debounce(handleWindowResize, 250);

        // \u521B\u5EFA\u8282\u6D41\u7248\u672C\u7684resize\u5904\u7406\u51FD\u6570\uFF08\u7528\u4E8E\u5373\u65F6\u53CD\u9988\uFF09
        const throttledResize = throttle(() => {
            // \u7ACB\u5373\u66F4\u65B0\u6392\u884C\u699C\u663E\u793A\u72B6\u6001\uFF0C\u63D0\u4F9B\u5373\u65F6\u53CD\u9988
            updateLayoutForDifficulty(game.currentDifficulty);
        }, 100);

        function setDifficulty(difficulty) {
            // \u66F4\u65B0\u6309\u94AE\u72B6\u6001
            document.querySelectorAll('.difficulty-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // \u5148\u66F4\u65B0\u96BE\u5EA6
            game.currentDifficulty = difficulty;

            // \u540C\u6B65\u5207\u6362\u6392\u884C\u699C\u96BE\u5EA6
            if (window.leaderboardManager) {
                window.leaderboardManager.switchDifficulty(difficulty);
            }

            // \u83B7\u53D6\u65B0\u96BE\u5EA6\u7684\u914D\u7F6E
            const config = game.difficulties[difficulty];

            // \u7ACB\u5373\u66F4\u65B0\u6E38\u620F\u5C3A\u5BF8
            game.rows = config.rows;
            game.cols = config.cols;
            game.mineCount = config.mines;

            // \u68C0\u67E5\u662F\u5426\u9700\u8981\u8C03\u6574\u6392\u884C\u699C\u663E\u793A
            updateLayoutForDifficulty(difficulty);

            // \u7ACB\u5373\u6E05\u7A7A\u6E38\u620F\u677F
            const boardGrid = document.getElementById('board-grid');
            boardGrid.innerHTML = '';
            boardGrid.removeAttribute('style');
            boardGrid.className = '';

            // \u5F3A\u5236\u91CD\u65B0\u8BA1\u7B97CSS\u53D8\u91CF
            game.calculateOptimalCellSize();

            // \u591A\u6B21\u5F3A\u5236\u5237\u65B0\u786E\u4FDDCSS\u53D8\u91CF\u751F\u6548
            for (let i = 0; i < 5; i++) {
                document.body.offsetHeight;
                document.documentElement.offsetHeight;
            }

            // \u4F7F\u7528\u591A\u91CD\u5F02\u6B65\u786E\u4FDD\u5B8C\u5168\u66F4\u65B0
            setTimeout(() => {
                requestAnimationFrame(() => {
                    setTimeout(() => {
                        // \u5B8C\u6574\u91CD\u65B0\u521D\u59CB\u5316\u6E38\u620F
                        game.initGame();
                    }, 10);
                });
            }, 10);
        }

        // \u6839\u636E\u96BE\u5EA6\u8C03\u6574\u5E03\u5C40
        function updateLayoutForDifficulty(difficulty) {
            const leaderboardPanel = document.querySelector('.leaderboard-panel');
            const leaderboardToggle = document.querySelector('.leaderboard-toggle');
            const viewportWidth = window.innerWidth;

            // \u786E\u4FDD\u6392\u884C\u699C\u5728\u5927\u5C4F\u5E55\u4E0A\u663E\u793A
            if (viewportWidth >= 1000) {
                if (leaderboardPanel) {
                    leaderboardPanel.style.display = 'block';
                }
                if (leaderboardToggle) {
                    leaderboardToggle.style.display = 'none';
                }
            } else {
                // \u5C0F\u5C4F\u5E55\u65F6\u663E\u793A\u5207\u6362\u6309\u94AE
                if (leaderboardToggle) {
                    leaderboardToggle.style.display = 'inline-block';
                }
            }

            // \u4F7F\u7528\u9632\u6296\u673A\u5236\u91CD\u65B0\u8BA1\u7B97\u5E03\u5C40\uFF0C\u907F\u514D\u9891\u7E41\u8C03\u7528
            if (game && game.calculateOptimalCellSize) {
                debouncedResize();
            }
        }
        
        function newGame() {
            game.initGame();
        }
        
        function showHelp() {
            const modal = document.getElementById('help-modal');
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        // \u5173\u95ED\u63D0\u793A\u6846
        function closeModal() {
            const modal = document.getElementById('game-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // \u4ECE\u63D0\u793A\u6846\u5F00\u59CB\u65B0\u6E38\u620F
        function newGameFromModal() {
            closeModal();
            newGame();
        }

        // \u5173\u95ED\u5E2E\u52A9\u63D0\u793A\u6846
        function closeHelpModal() {
            const modal = document.getElementById('help-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // \u6392\u884C\u699C\u76F8\u5173\u51FD\u6570
        function switchLeaderboardDifficulty(difficulty) {
            if (window.leaderboardManager) {
                window.leaderboardManager.switchDifficulty(difficulty);
            }
        }

        function toggleLeaderboard() {
            const leaderboardPanel = document.querySelector('.leaderboard-panel');
            const toggleBtn = document.getElementById('leaderboard-toggle');

            if (leaderboardPanel) {
                if (leaderboardPanel.style.display === 'none') {
                    leaderboardPanel.style.display = 'block';
                    toggleBtn.textContent = '\u274C \u9690\u85CF\u6392\u884C\u699C';
                    // \u52A0\u8F7D\u6392\u884C\u699C\u6570\u636E
                    if (window.leaderboardManager) {
                        window.leaderboardManager.loadLeaderboard('beginner');
                    }
                } else {
                    leaderboardPanel.style.display = 'none';
                    toggleBtn.textContent = '\u{1F4CA} \u6392\u884C\u699C';
                }
                // \u4F7F\u7528\u9632\u6296\u673A\u5236\u91CD\u65B0\u8BA1\u7B97\u5E03\u5C40
                debouncedResize();
            }
        }

        // \u6210\u7EE9\u4E0A\u4F20\u76F8\u5173\u51FD\u6570
        function showUploadDialog() {
            if (!window.currentScore) return;

            const modal = document.getElementById('upload-modal');
            const uploadInfo = document.getElementById('upload-info');
            const usernameInput = document.getElementById('username-input');

            // \u66F4\u65B0\u4E0A\u4F20\u4FE1\u606F\u663E\u793A
            const difficultyNames = {
                'beginner': '\u521D\u7EA7',
                'intermediate': '\u4E2D\u7EA7',
                'expert': '\u4E13\u5BB6'
            };

            uploadInfo.innerHTML = '\u7528\u65F6\uFF1A' + window.currentScore.time + '\u79D2<br>\u96BE\u5EA6\uFF1A' + difficultyNames[window.currentScore.difficulty];

            // \u6E05\u7A7A\u8F93\u5165\u6846
            usernameInput.value = '';
            usernameInput.focus();

            // \u663E\u793A\u5BF9\u8BDD\u6846
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        function closeUploadModal() {
            const modal = document.getElementById('upload-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        async function submitScore() {
            const usernameInput = document.getElementById('username-input');
            const submitBtn = document.querySelector('#upload-modal .modal-button.primary');
            const submitText = document.getElementById('submit-text');
            const submitSpinner = document.getElementById('submit-spinner');

            const username = usernameInput.value.trim();

            // \u9A8C\u8BC1\u7528\u6237\u540D
            if (!username) {
                alert('\u8BF7\u8F93\u5165\u7528\u6237\u540D');
                usernameInput.focus();
                return;
            }

            if (username.length > 20) {
                alert('\u7528\u6237\u540D\u4E0D\u80FD\u8D85\u8FC720\u4E2A\u5B57\u7B26');
                usernameInput.focus();
                return;
            }

            if (!/^[a-zA-Z0-9\u4E00-\u9FA5_-]+$/.test(username)) {
                alert('\u7528\u6237\u540D\u53EA\u80FD\u5305\u542B\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E2D\u6587\u3001\u4E0B\u5212\u7EBF\u548C\u8FDE\u5B57\u7B26');
                usernameInput.focus();
                return;
            }

            // \u663E\u793A\u52A0\u8F7D\u72B6\u6001
            submitBtn.disabled = true;
            submitText.style.display = 'none';
            submitSpinner.style.display = 'inline-block';

            try {
                const result = await window.leaderboardManager.uploadScore(
                    username,
                    window.currentScore.time,
                    window.currentScore.difficulty
                );

                if (result.success) {
                    // \u4E0A\u4F20\u6210\u529F
                    closeUploadModal();
                    closeModal();
                    alert('\u{1F389} \u6210\u7EE9\u4E0A\u4F20\u6210\u529F\uFF01
\u5FEB\u53BB\u6392\u884C\u699C\u770B\u770B\u4F60\u7684\u6392\u540D\u5427\uFF01');
                } else {
                    // \u4E0A\u4F20\u5931\u8D25
                    alert('\u4E0A\u4F20\u5931\u8D25\uFF1A' + (result.error || '\u672A\u77E5\u9519\u8BEF'));
                }
            } catch (error) {
                console.error('\u63D0\u4EA4\u6210\u7EE9\u9519\u8BEF:', error);
                alert('\u4E0A\u4F20\u5931\u8D25\uFF1A\u7F51\u7EDC\u8FDE\u63A5\u9519\u8BEF');
            } finally {
                // \u6062\u590D\u6309\u94AE\u72B6\u6001
                submitBtn.disabled = false;
                submitText.style.display = 'inline';
                submitSpinner.style.display = 'none';
            }
        }

        // \u9875\u9762\u52A0\u8F7D\u5B8C\u6210\u540E\u521D\u59CB\u5316
        document.addEventListener('DOMContentLoaded', () => {
            // \u521D\u59CB\u5316\u6392\u884C\u699C
            if (window.leaderboardManager) {
                window.leaderboardManager.loadLeaderboard('beginner');
            }

            // \u786E\u4FDD\u6E38\u620F\u6B63\u786E\u521D\u59CB\u5316
            if (game) {
                game.initGame();
            }

            // \u6DFB\u52A0\u7A97\u53E3\u5927\u5C0F\u53D8\u5316\u4E8B\u4EF6\u76D1\u542C\u5668
            window.addEventListener('resize', () => {
                // \u4F7F\u7528\u8282\u6D41\u51FD\u6570\u63D0\u4F9B\u5373\u65F6\u53CD\u9988
                throttledResize();
                // \u4F7F\u7528\u9632\u6296\u51FD\u6570\u8FDB\u884C\u6700\u7EC8\u7684\u7CBE\u786E\u8BA1\u7B97
                debouncedResize();
            });

            // \u6DFB\u52A0\u65B9\u5411\u53D8\u5316\u4E8B\u4EF6\u76D1\u542C\u5668\uFF08\u79FB\u52A8\u8BBE\u5907\uFF09
            window.addEventListener('orientationchange', () => {
                // \u65B9\u5411\u53D8\u5316\u540E\u7A0D\u5FAE\u5EF6\u8FDF\uFF0C\u7B49\u5F85\u6D4F\u89C8\u5668\u5B8C\u6210\u5E03\u5C40\u8C03\u6574
                setTimeout(() => {
                    debouncedResize();
                }, 300);
            });

            console.log('\u7A97\u53E3\u5927\u5C0F\u53D8\u5316\u76D1\u542C\u5668\u5DF2\u521D\u59CB\u5316');
        });

        // \u652F\u6301\u56DE\u8F66\u952E\u63D0\u4EA4\u6210\u7EE9
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                const uploadModal = document.getElementById('upload-modal');
                if (uploadModal && uploadModal.style.display === 'flex') {
                    submitScore();
                }
            }
        });
    <\/script>
</body>
</html>`;
}
__name(getGameHTML, "getGameHTML");

// node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-hQIGZL/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-hQIGZL/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof __Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
__name(__Facade_ScheduledController__, "__Facade_ScheduledController__");
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = (request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    };
    #dispatcher = (type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    };
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=index.js.map
