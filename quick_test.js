// 快速测试排行榜API
async function testAPI() {
    const baseURL = 'http://127.0.0.1:8787/api/leaderboard';
    
    console.log('🧪 开始测试排行榜API...');
    
    try {
        // 测试1: 获取初级排行榜
        console.log('\n📊 测试1: 获取初级排行榜');
        const getResponse = await fetch(`${baseURL}/beginner`);
        const getResult = await getResponse.json();
        console.log('结果:', getResult);
        
        // 测试2: 上传一个成绩
        console.log('\n📤 测试2: 上传成绩');
        const uploadResponse = await fetch(`${baseURL}/beginner`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: '测试玩家',
                time: 120
            })
        });
        const uploadResult = await uploadResponse.json();
        console.log('结果:', uploadResult);
        
        // 测试3: 再次获取排行榜，验证数据已保存
        console.log('\n📊 测试3: 验证数据已保存');
        const verifyResponse = await fetch(`${baseURL}/beginner`);
        const verifyResult = await verifyResponse.json();
        console.log('结果:', verifyResult);
        
        console.log('\n✅ 所有测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
    // Node.js环境
    const fetch = require('node-fetch');
    testAPI();
} else {
    // 浏览器环境
    window.testAPI = testAPI;
    console.log('在浏览器控制台中运行: testAPI()');
}
