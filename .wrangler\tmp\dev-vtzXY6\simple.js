var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// .wrangler/tmp/bundle-JYa50Q/checked-fetch.js
var urls = /* @__PURE__ */ new Set();
function checkURL(request, init) {
  const url = request instanceof URL ? request : new URL(
    (typeof request === "string" ? new Request(request, init) : request).url
  );
  if (url.port && url.port !== "443" && url.protocol === "https:") {
    if (!urls.has(url.toString())) {
      urls.add(url.toString());
      console.warn(
        `WARNING: known issue with \`fetch()\` requests to custom HTTPS ports in published Workers:
 - ${url.toString()} - the custom port will be ignored when the Worker is published using the \`wrangler deploy\` command.
`
      );
    }
  }
}
__name(checkURL, "checkURL");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    const [request, init] = argArray;
    checkURL(request, init);
    return Reflect.apply(target, thisArg, argArray);
  }
});

// .wrangler/tmp/bundle-JYa50Q/strip-cf-connecting-ip-header.js
function stripCfConnectingIPHeader(input, init) {
  const request = new Request(input, init);
  request.headers.delete("CF-Connecting-IP");
  return request;
}
__name(stripCfConnectingIPHeader, "stripCfConnectingIPHeader");
globalThis.fetch = new Proxy(globalThis.fetch, {
  apply(target, thisArg, argArray) {
    return Reflect.apply(target, thisArg, [
      stripCfConnectingIPHeader.apply(null, argArray)
    ]);
  }
});

// src/simple.js
async function handleLeaderboardAPI(request, env, url) {
  const difficulty = url.pathname.split("/").pop();
  if (request.method === "GET") {
    try {
      const data = await env.LEADERBOARD.get("leaderboard:" + difficulty);
      const leaderboard = data ? JSON.parse(data) : [];
      return new Response(JSON.stringify({ success: true, data: leaderboard }), {
        headers: { "Content-Type": "application/json" }
      });
    } catch (error) {
      return new Response(JSON.stringify({ success: false, error: error.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }
  }
  if (request.method === "POST") {
    try {
      const { username, time } = await request.json();
      if (!username || !time) {
        return new Response(JSON.stringify({ success: false, error: "\u7F3A\u5C11\u5FC5\u8981\u53C2\u6570" }), {
          status: 400,
          headers: { "Content-Type": "application/json" }
        });
      }
      const data = await env.LEADERBOARD.get("leaderboard:" + difficulty);
      const leaderboard = data ? JSON.parse(data) : [];
      leaderboard.push({ username, time, date: (/* @__PURE__ */ new Date()).toISOString() });
      leaderboard.sort((a, b) => a.time - b.time);
      const top10 = leaderboard.slice(0, 10);
      await env.LEADERBOARD.put("leaderboard:" + difficulty, JSON.stringify(top10));
      return new Response(JSON.stringify({ success: true, data: top10 }), {
        headers: { "Content-Type": "application/json" }
      });
    } catch (error) {
      return new Response(JSON.stringify({ success: false, error: error.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" }
      });
    }
  }
  return new Response("Method not allowed", { status: 405 });
}
__name(handleLeaderboardAPI, "handleLeaderboardAPI");
var simple_default = {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    if (url.pathname.startsWith("/api/leaderboard/")) {
      return handleLeaderboardAPI(request, env, url);
    }
    if (url.pathname === "/") {
      return new Response(getGameHTML(), {
        headers: {
          "Content-Type": "text/html;charset=UTF-8",
          "Cache-Control": "public, max-age=3600"
        }
      });
    }
    return new Response("Not Found", { status: 404 });
  }
};
function getGameHTML() {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>\u7ECF\u5178\u626B\u96F7 - Classic Minesweeper</title>
    <style>
        :root {
            --cell-size: 30px;
            --counter-font-size: 24px;
            --smiley-size: 40px;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'MS Sans Serif', sans-serif;
            background-color: #c0c0c0;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            user-select: none;
            padding: 20px 10px;
        }
        .main-container {
            display: flex;
            gap: 20px;
            width: 100%;
            max-width: 1600px;
            align-items: flex-start;
            justify-content: center;
        }
        .game-container {
            flex: 1;
            min-width: 400px;
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .difficulty-selector {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .difficulty-buttons {
            display: flex;
            gap: 5px;
        }
        .difficulty-button {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            min-width: 60px;
        }
        .difficulty-button:active {
            border: 2px inset #c0c0c0;
        }
        .difficulty-button.active {
            border: 2px inset #c0c0c0;
            background-color: #a0a0a0;
        }
        .help-button {
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
        }
        .help-button:active {
            border: 2px inset #c0c0c0;
        }
        .game-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #c0c0c0;
            border: 2px inset #c0c0c0;
            padding: 3px 6px;
            margin-bottom: 10px;
            width: 100%;
        }
        .counter {
            background-color: #000;
            color: #ff0000;
            font-family: 'Courier New', monospace;
            font-size: var(--counter-font-size);
            font-weight: bold;
            padding: 3px 6px;
            border: 1px inset #808080;
            min-width: calc(var(--counter-font-size) * 2.5);
            text-align: center;
        }
        .smiley-button {
            width: var(--smiley-size);
            height: var(--smiley-size);
            font-size: calc(var(--smiley-size) * 0.6);
            background-color: #c0c0c0;
            border: 2px outset #c0c0c0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .smiley-button:active {
            border: 2px inset #c0c0c0;
        }
        .game-board {
            border: 2px inset #c0c0c0;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #c0c0c0;
            padding: 2px;
        }
        .board-grid {
            display: grid;
            gap: 0;
            border: 1px solid #808080;
        }
        .cell {
            width: var(--cell-size);
            height: var(--cell-size);
            border: 1px outset #c0c0c0;
            background-color: #c0c0c0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: calc(var(--cell-size) * 0.6);
            font-weight: bold;
            cursor: pointer;
        }
        .cell:active {
            border: 1px inset #c0c0c0;
        }
        .cell.revealed {
            border: 1px solid #808080;
            background-color: #ffffff;
        }
        .cell.mine {
            background-color: #ff0000;
        }
        .cell.flagged::after {
            content: '\u{1F6A9}';
            font-size: calc(var(--cell-size) * 0.7);
        }
        .cell.number-1 { color: #0000ff; }
        .cell.number-2 { color: #008000; }
        .cell.number-3 { color: #ff0000; }
        .cell.number-4 { color: #000080; }
        .cell.number-5 { color: #800000; }
        .cell.number-6 { color: #008080; }
        .cell.number-7 { color: #000000; }
        .cell.number-8 { color: #808080; }
        
        /* \u6392\u884C\u699C\u9762\u677F\u6837\u5F0F */
        .leaderboard-panel {
            width: 280px;
            background-color: #c0c0c0;
            border: 2px inset #c0c0c0;
            padding: 10px;
            height: fit-content;
            max-height: 80vh;
            overflow-y: auto;
        }
        .leaderboard-header h3 {
            margin: 0 0 10px 0;
            font-size: 16px;
            text-align: center;
            color: #000080;
        }
        .leaderboard-tabs {
            display: flex;
            gap: 2px;
            margin-bottom: 15px;
        }
        .tab-button {
            flex: 1;
            padding: 6px 4px;
            font-size: 11px;
            background: #c0c0c0;
            border: 1px outset #c0c0c0;
            cursor: pointer;
        }
        .tab-button.active {
            background: #ffffff;
            border: 1px inset #c0c0c0;
        }
        .leaderboard-list {
            display: block;
        }
        .leaderboard-item {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            margin: 2px 0;
            background: #f0f0f0;
            border: 1px solid #808080;
            font-size: 12px;
        }
        .leaderboard-rank {
            font-weight: bold;
            color: #666;
            min-width: 25px;
            text-align: center;
        }
        .leaderboard-username {
            flex: 1;
            margin: 0 8px;
            font-weight: bold;
        }
        .leaderboard-time {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #d00;
            font-size: 11px;
        }
        
        /* \u7F8E\u5316\u7684\u6A21\u6001\u6846\u6837\u5F0F */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.3s ease-out;
        }

        .modal-content {
            background-color: #c0c0c0;
            margin: 15% auto;
            padding: 0;
            border: 3px outset #c0c0c0;
            width: 90%;
            max-width: 400px;
            border-radius: 0;
            animation: slideIn 0.3s ease-out;
        }

        .modal-header {
            background: linear-gradient(90deg, #000080, #0000ff);
            color: white;
            padding: 8px 12px;
            font-weight: bold;
            font-size: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-close {
            background: #c0c0c0;
            border: 1px outset #c0c0c0;
            width: 20px;
            height: 18px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:active {
            border: 1px inset #c0c0c0;
        }

        .modal-body {
            padding: 20px;
            text-align: center;
            font-size: 14px;
            line-height: 1.5;
        }

        .modal-icon {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }

        .modal-buttons {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .modal-button {
            background: #c0c0c0;
            border: 2px outset #c0c0c0;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            min-width: 80px;
        }

        .modal-button:active {
            border: 2px inset #c0c0c0;
        }

        .modal-button.primary {
            background: #0078d4;
            color: white;
            border: 2px outset #0078d4;
        }

        .modal-button.primary:active {
            border: 2px inset #0078d4;
        }

        .modal-input {
            width: 100%;
            padding: 6px;
            border: 2px inset #c0c0c0;
            font-size: 14px;
            margin: 10px 0;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @media (max-width: 768px) {
            .leaderboard-panel {
                display: none;
            }
            .modal-content {
                width: 95%;
                margin: 20% auto;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- \u6392\u884C\u699C\u9762\u677F -->
        <div class="leaderboard-panel">
            <div class="leaderboard-header">
                <h3>\u{1F3C6} \u6392\u884C\u699C</h3>
                <div class="leaderboard-tabs">
                    <button class="tab-button active" onclick="switchLeaderboard('beginner')">\u521D\u7EA7</button>
                    <button class="tab-button" onclick="switchLeaderboard('intermediate')">\u4E2D\u7EA7</button>
                    <button class="tab-button" onclick="switchLeaderboard('expert')">\u4E13\u5BB6</button>
                </div>
            </div>
            <div class="leaderboard-list" id="leaderboard-list">
                <div style="text-align: center; padding: 20px; color: #666;">\u52A0\u8F7D\u4E2D...</div>
            </div>
        </div>

        <!-- \u6E38\u620F\u533A\u57DF -->
        <div class="game-container">
            <div class="difficulty-selector">
                <div class="difficulty-buttons">
                    <button class="difficulty-button active" onclick="setDifficulty('beginner')">\u521D\u7EA7</button>
                    <button class="difficulty-button" onclick="setDifficulty('intermediate')">\u4E2D\u7EA7</button>
                    <button class="difficulty-button" onclick="setDifficulty('expert')">\u4E13\u5BB6</button>
                </div>
                <button class="help-button" onclick="showHelp()">\u5E2E\u52A9</button>
            </div>

            <div class="game-header">
                <div class="counter" id="mine-counter">010</div>
                <button class="smiley-button" id="smiley-button" onclick="newGame()">\u{1F60A}</button>
                <div class="counter" id="timer">000</div>
            </div>

            <div class="game-board">
                <div class="board-grid" id="board-grid"></div>
            </div>
        </div>
    </div>

    <!-- \u7F8E\u5316\u7684\u6A21\u6001\u6846 -->
    <div id="game-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span id="modal-title">\u6E38\u620F\u63D0\u793A</span>
                <button class="modal-close" onclick="closeModal()">\xD7</button>
            </div>
            <div class="modal-body">
                <span id="modal-icon" class="modal-icon">\u{1F60A}</span>
                <div id="modal-message">\u6D88\u606F\u5185\u5BB9</div>
                <div id="modal-input-container" style="display: none;">
                    <input type="text" id="modal-input" class="modal-input" placeholder="\u8BF7\u8F93\u5165\u60A8\u7684\u7528\u6237\u540D" maxlength="20">
                </div>
                <div class="modal-buttons">
                    <button id="modal-cancel" class="modal-button" onclick="closeModal()" style="display: none;">\u53D6\u6D88</button>
                    <button id="modal-confirm" class="modal-button primary" onclick="handleModalConfirm()">\u786E\u5B9A</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // \u7B80\u5316\u7684\u626B\u96F7\u6E38\u620F
        class MinesweeperGame {
            constructor() {
                this.difficulties = {
                    beginner: { rows: 9, cols: 9, mines: 10 },
                    intermediate: { rows: 16, cols: 16, mines: 40 },
                    expert: { rows: 16, cols: 30, mines: 99 }
                };
                this.currentDifficulty = 'beginner';
                this.board = [];
                this.revealed = [];
                this.flagged = [];
                this.gameState = 'ready';
                this.firstClick = true;
                this.startTime = null;
                this.timer = null;
                this.mineCount = 0;
                this.flagCount = 0;
            }

            initGame() {
                const config = this.difficulties[this.currentDifficulty];
                this.rows = config.rows;
                this.cols = config.cols;
                this.mineCount = config.mines;
                this.flagCount = 0;

                this.board = Array(this.rows).fill().map(() => Array(this.cols).fill(0));
                this.revealed = Array(this.rows).fill().map(() => Array(this.cols).fill(false));
                this.flagged = Array(this.rows).fill().map(() => Array(this.cols).fill(false));

                this.gameState = 'ready';
                this.firstClick = true;
                this.startTime = null;

                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }

                this.createBoard();
                this.updateDisplay();

                document.getElementById('smiley-button').textContent = '\u{1F60A}';
                document.getElementById('timer').textContent = '000';
            }

            createBoard() {
                const boardGrid = document.getElementById('board-grid');
                boardGrid.innerHTML = '';

                // \u52A8\u6001\u8BA1\u7B97\u683C\u5B50\u5927\u5C0F
                this.calculateOptimalCellSize();

                boardGrid.style.gridTemplateColumns = 'repeat(' + this.cols + ', var(--cell-size))';
                boardGrid.style.gridTemplateRows = 'repeat(' + this.rows + ', var(--cell-size))';

                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        const cell = document.createElement('div');
                        cell.className = 'cell';
                        cell.addEventListener('click', (e) => this.handleLeftClick(row, col, e));
                        cell.addEventListener('contextmenu', (e) => this.handleRightClick(row, col, e));
                        boardGrid.appendChild(cell);
                    }
                }
            }

            // \u8BA1\u7B97\u6700\u4F73\u683C\u5B50\u5927\u5C0F\uFF0C\u786E\u4FDD\u6240\u6709\u5185\u5BB9\u90FD\u5728\u89C6\u53E3\u5185
            calculateOptimalCellSize() {
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                // \u7CBE\u786E\u6D4B\u91CF\u5B9E\u9645\u5360\u7528\u7684\u7A7A\u95F4
                const leaderboardPanel = document.querySelector('.leaderboard-panel');
                let leaderboardWidth = 0;
                if (leaderboardPanel && window.getComputedStyle(leaderboardPanel).display !== 'none') {
                    leaderboardWidth = leaderboardPanel.offsetWidth + 20; // \u5305\u62EC\u95F4\u8DDD
                }

                // \u7CBE\u786E\u6D4B\u91CF\u9876\u90E8\u63A7\u4EF6\u9AD8\u5EA6
                const difficultySelector = document.querySelector('.difficulty-selector');
                const gameHeader = document.querySelector('.game-header');
                let topHeight = 0;
                if (difficultySelector && gameHeader) {
                    topHeight = difficultySelector.offsetHeight + gameHeader.offsetHeight + 30; // \u5305\u62EC\u8FB9\u8DDD
                }

                // \u8BA1\u7B97\u6E38\u620F\u677F\u7684\u6700\u5927\u53EF\u7528\u7A7A\u95F4\uFF08\u7559\u51FA\u5B89\u5168\u8FB9\u8DDD\uFF09
                const safeMargin = 20;
                const maxBoardWidth = viewportWidth - leaderboardWidth - safeMargin * 2;
                const maxBoardHeight = viewportHeight - topHeight - safeMargin * 2;

                // \u786E\u4FDD\u6700\u5C0F\u53EF\u7528\u7A7A\u95F4
                const minBoardWidth = Math.max(maxBoardWidth, 200);
                const minBoardHeight = Math.max(maxBoardHeight, 150);

                // \u8BA1\u7B97\u683C\u5B50\u5927\u5C0F\uFF0C\u786E\u4FDD\u6E38\u620F\u677F\u5B8C\u5168\u9002\u5E94\u53EF\u7528\u7A7A\u95F4
                const maxCellSizeByWidth = Math.floor(minBoardWidth / this.cols);
                const maxCellSizeByHeight = Math.floor(minBoardHeight / this.rows);

                // \u53D6\u8F83\u5C0F\u503C\u786E\u4FDD\u5B8C\u6574\u663E\u793A
                let optimalSize = Math.min(maxCellSizeByWidth, maxCellSizeByHeight);

                // \u8BBE\u7F6E\u5408\u7406\u7684\u5927\u5C0F\u8303\u56F4\uFF0C\u4F46\u4F18\u5148\u4FDD\u8BC1\u5B8C\u6574\u663E\u793A
                optimalSize = Math.max(12, Math.min(45, optimalSize));

                // \u6700\u7EC8\u9A8C\u8BC1\uFF1A\u786E\u4FDD\u8BA1\u7B97\u51FA\u7684\u6E38\u620F\u677F\u5C3A\u5BF8\u4E0D\u4F1A\u8D85\u51FA\u89C6\u53E3
                const finalBoardWidth = optimalSize * this.cols;
                const finalBoardHeight = optimalSize * this.rows;
                const totalWidth = finalBoardWidth + leaderboardWidth + safeMargin * 2;
                const totalHeight = finalBoardHeight + topHeight + safeMargin * 2;

                // \u5982\u679C\u4ECD\u7136\u8D85\u51FA\uFF0C\u8FDB\u4E00\u6B65\u7F29\u5C0F
                if (totalWidth > viewportWidth || totalHeight > viewportHeight) {
                    const scaleX = viewportWidth / totalWidth;
                    const scaleY = viewportHeight / totalHeight;
                    const scale = Math.min(scaleX, scaleY) * 0.95; // \u75595%\u5B89\u5168\u8FB9\u8DDD
                    optimalSize = Math.floor(optimalSize * scale);
                    optimalSize = Math.max(10, optimalSize); // \u7EDD\u5BF9\u6700\u5C0F\u503C
                }

                // \u66F4\u65B0CSS\u53D8\u91CF
                document.documentElement.style.setProperty('--cell-size', optimalSize + 'px');
                document.documentElement.style.setProperty('--counter-font-size', Math.max(12, optimalSize * 0.7) + 'px');
                document.documentElement.style.setProperty('--smiley-size', Math.max(24, optimalSize * 1.0) + 'px');

                console.log('\u5E03\u5C40\u8BA1\u7B97\u7ED3\u679C:', {
                    \u89C6\u53E3: viewportWidth + 'x' + viewportHeight,
                    \u6392\u884C\u699C\u5BBD\u5EA6: leaderboardWidth,
                    \u9876\u90E8\u9AD8\u5EA6: topHeight,
                    \u53EF\u7528\u7A7A\u95F4: minBoardWidth + 'x' + minBoardHeight,
                    \u6E38\u620F\u5C3A\u5BF8: this.cols + 'x' + this.rows,
                    \u683C\u5B50\u5927\u5C0F: optimalSize,
                    \u6700\u7EC8\u6E38\u620F\u677F: finalBoardWidth + 'x' + finalBoardHeight
                });
            }

            generateMines(firstClickRow, firstClickCol) {
                const positions = [];
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (Math.abs(row - firstClickRow) <= 1 && Math.abs(col - firstClickCol) <= 1) {
                            continue;
                        }
                        positions.push([row, col]);
                    }
                }

                for (let i = positions.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [positions[i], positions[j]] = [positions[j], positions[i]];
                }

                for (let i = 0; i < this.mineCount && i < positions.length; i++) {
                    const [row, col] = positions[i];
                    this.board[row][col] = -1;
                }

                this.calculateNumbers();
            }

            calculateNumbers() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (this.board[row][col] !== -1) {
                            let count = 0;
                            for (let dr = -1; dr <= 1; dr++) {
                                for (let dc = -1; dc <= 1; dc++) {
                                    const newRow = row + dr;
                                    const newCol = col + dc;
                                    if (this.isValidCell(newRow, newCol) && this.board[newRow][newCol] === -1) {
                                        count++;
                                    }
                                }
                            }
                            this.board[row][col] = count;
                        }
                    }
                }
            }

            isValidCell(row, col) {
                return row >= 0 && row < this.rows && col >= 0 && col < this.cols;
            }

            handleLeftClick(row, col, event) {
                event.preventDefault();
                if (this.gameState === 'won' || this.gameState === 'lost') return;
                if (this.flagged[row][col]) return;

                if (this.firstClick) {
                    this.generateMines(row, col);
                    this.firstClick = false;
                    this.gameState = 'playing';
                    this.startTimer();
                }

                this.revealCell(row, col);
                this.updateDisplay();
                this.checkGameState();
            }

            handleRightClick(row, col, event) {
                event.preventDefault();
                if (this.gameState === 'won' || this.gameState === 'lost') return;
                if (this.revealed[row][col]) return;

                this.flagged[row][col] = !this.flagged[row][col];
                this.flagCount += this.flagged[row][col] ? 1 : -1;
                this.updateDisplay();
            }

            revealCell(row, col) {
                if (!this.isValidCell(row, col) || this.revealed[row][col] || this.flagged[row][col]) {
                    return;
                }

                this.revealed[row][col] = true;

                if (this.board[row][col] === -1) {
                    this.gameState = 'lost';
                    this.revealAllMines();
                    return;
                }

                if (this.board[row][col] === 0) {
                    for (let dr = -1; dr <= 1; dr++) {
                        for (let dc = -1; dc <= 1; dc++) {
                            this.revealCell(row + dr, col + dc);
                        }
                    }
                }
            }

            revealAllMines() {
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (this.board[row][col] === -1) {
                            this.revealed[row][col] = true;
                        }
                    }
                }
            }

            checkGameState() {
                if (this.gameState === 'lost') {
                    document.getElementById('smiley-button').textContent = '\u{1F635}';
                    this.stopTimer();
                    setTimeout(() => {
                        showModal('\u6E38\u620F\u5931\u8D25', '\u{1F4A3}', '\u8E29\u5230\u5730\u96F7\u4E86\uFF01<br>\u70B9\u51FB\u7B11\u8138\u6216\u65B0\u6E38\u620F\u6309\u94AE\u91CD\u65B0\u5F00\u59CB\u3002');
                    }, 100);
                    return;
                }

                let unrevealedCount = 0;
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        if (!this.revealed[row][col] && this.board[row][col] !== -1) {
                            unrevealedCount++;
                        }
                    }
                }

                if (unrevealedCount === 0) {
                    this.gameState = 'won';
                    document.getElementById('smiley-button').textContent = '\u{1F60E}';
                    this.stopTimer();

                    for (let row = 0; row < this.rows; row++) {
                        for (let col = 0; col < this.cols; col++) {
                            if (this.board[row][col] === -1 && !this.flagged[row][col]) {
                                this.flagged[row][col] = true;
                                this.flagCount++;
                            }
                        }
                    }

                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                    setTimeout(async () => {
                        const message = '\u7528\u65F6\uFF1A' + elapsed + '\u79D2<br>\u96BE\u5EA6\uFF1A' + this.getDifficultyName() + '<br><br>\u592A\u68D2\u4E86\uFF01\u4F60\u6210\u529F\u627E\u51FA\u4E86\u6240\u6709\u5730\u96F7\uFF01<br>\u8BF7\u8F93\u5165\u60A8\u7684\u7528\u6237\u540D\u4E0A\u4F20\u6210\u7EE9\uFF1A';
                        const username = await showModal('\u606D\u559C\u80DC\u5229\uFF01', '\u{1F389}', message, true, true);
                        if (username && username.trim()) {
                            uploadScore(username.trim(), elapsed, this.currentDifficulty);
                        }
                    }, 100);
                }
            }

            startTimer() {
                this.startTime = Date.now();
                this.timer = setInterval(() => {
                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                    const displayTime = Math.min(elapsed, 999);
                    document.getElementById('timer').textContent = displayTime.toString().padStart(3, '0');
                }, 1000);
            }

            stopTimer() {
                if (this.timer) {
                    clearInterval(this.timer);
                    this.timer = null;
                }
            }

            getDifficultyName() {
                const names = {
                    beginner: '\u521D\u7EA7',
                    intermediate: '\u4E2D\u7EA7',
                    expert: '\u4E13\u5BB6'
                };
                return names[this.currentDifficulty] || '\u672A\u77E5';
            }

            updateDisplay() {
                const remainingMines = this.mineCount - this.flagCount;
                document.getElementById('mine-counter').textContent =
                    Math.max(-99, Math.min(999, remainingMines)).toString().padStart(3, '0');

                const cells = document.querySelectorAll('.cell');
                cells.forEach((cell, index) => {
                    const row = Math.floor(index / this.cols);
                    const col = index % this.cols;

                    cell.className = 'cell';
                    cell.textContent = '';

                    if (this.flagged[row][col]) {
                        cell.classList.add('flagged');
                    } else if (this.revealed[row][col]) {
                        cell.classList.add('revealed');
                        if (this.board[row][col] === -1) {
                            cell.classList.add('mine');
                            cell.textContent = '\u{1F4A3}';
                        } else if (this.board[row][col] > 0) {
                            cell.classList.add('number-' + this.board[row][col]);
                            cell.textContent = this.board[row][col];
                        }
                    }
                });
            }
        }

        // \u5168\u5C40\u53D8\u91CF
        let game = null;
        let currentLeaderboardDifficulty = 'beginner';
        let modalCallback = null;

        // \u6A21\u6001\u6846\u51FD\u6570
        function showModal(title, icon, message, showInput = false, showCancel = false) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-icon').textContent = icon;
            document.getElementById('modal-message').innerHTML = message;

            const inputContainer = document.getElementById('modal-input-container');
            const cancelButton = document.getElementById('modal-cancel');
            const input = document.getElementById('modal-input');

            if (showInput) {
                inputContainer.style.display = 'block';
                input.value = '';
                setTimeout(() => input.focus(), 100);
            } else {
                inputContainer.style.display = 'none';
            }

            cancelButton.style.display = showCancel ? 'inline-block' : 'none';

            document.getElementById('game-modal').style.display = 'block';

            return new Promise((resolve) => {
                modalCallback = resolve;
            });
        }

        function closeModal() {
            document.getElementById('game-modal').style.display = 'none';
            if (modalCallback) {
                modalCallback(null);
                modalCallback = null;
            }
        }

        function handleModalConfirm() {
            const input = document.getElementById('modal-input');
            const value = input.style.display !== 'none' && input.offsetParent !== null ? input.value.trim() : true;

            document.getElementById('game-modal').style.display = 'none';
            if (modalCallback) {
                modalCallback(value);
                modalCallback = null;
            }
        }

        // \u70B9\u51FB\u6A21\u6001\u6846\u80CC\u666F\u5173\u95ED
        document.addEventListener('click', (e) => {
            if (e.target.id === 'game-modal') {
                closeModal();
            }
        });

        // \u5168\u5C40\u51FD\u6570
        function setDifficulty(difficulty) {
            if (!game) return;

            document.querySelectorAll('.difficulty-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            game.currentDifficulty = difficulty;
            game.initGame();
        }

        function newGame() {
            if (game) {
                game.initGame();
            }
        }

        function showHelp() {
            const helpMessage =
                '<strong>\u684C\u9762\u7AEF\uFF1A</strong><br>' +
                '\u5DE6\u952E\u70B9\u51FB\u6316\u6398\u683C\u5B50<br>' +
                '\u53F3\u952E\u70B9\u51FB\u6807\u8BB0\u5730\u96F7<br><br>' +

                '<strong>\u79FB\u52A8\u7AEF\uFF1A</strong><br>' +
                '\u70B9\u51FB\u6316\u6398\u683C\u5B50<br>' +
                '\u957F\u6309\u6807\u8BB0\u5730\u96F7<br><br>' +

                '<strong>\u96BE\u5EA6\u9009\u62E9\uFF1A</strong><br>' +
                '\u2022 \u521D\u7EA7\uFF1A9\xD79\uFF0C10\u4E2A\u5730\u96F7<br>' +
                '\u2022 \u4E2D\u7EA7\uFF1A16\xD716\uFF0C40\u4E2A\u5730\u96F7<br>' +
                '\u2022 \u4E13\u5BB6\uFF1A30\xD716\uFF0C99\u4E2A\u5730\u96F7<br><br>' +

                '<strong>\u63D0\u793A\uFF1A</strong><br>' +
                '\u6570\u5B57\u8868\u793A\u5468\u56F48\u4E2A\u683C\u5B50\u4E2D\u5730\u96F7\u7684\u6570\u91CF';
            showModal('\u6E38\u620F\u5E2E\u52A9', '\u2753', helpMessage);
        }

        function switchLeaderboard(difficulty) {
            currentLeaderboardDifficulty = difficulty;

            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            loadLeaderboard(difficulty);
        }

        async function loadLeaderboard(difficulty) {
            try {
                const response = await fetch('/api/leaderboard/' + difficulty);
                const result = await response.json();

                const listElement = document.getElementById('leaderboard-list');
                if (result.success && result.data.length > 0) {
                    listElement.innerHTML = result.data.map((record, index) => {
                        const rank = index + 1;
                        return '<div class="leaderboard-item">' +
                               '<div class="leaderboard-rank">' + rank + '</div>' +
                               '<div class="leaderboard-username">' + record.username + '</div>' +
                               '<div class="leaderboard-time">' + record.time + 's</div>' +
                               '</div>';
                    }).join('');
                } else {
                    listElement.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">\u6682\u65E0\u8BB0\u5F55</div>';
                }
            } catch (error) {
                console.error('\u52A0\u8F7D\u6392\u884C\u699C\u5931\u8D25:', error);
                document.getElementById('leaderboard-list').innerHTML =
                    '<div style="text-align: center; padding: 20px; color: #d00;">\u52A0\u8F7D\u5931\u8D25</div>';
            }
        }

        async function uploadScore(username, time, difficulty) {
            try {
                const response = await fetch('/api/leaderboard/' + difficulty, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, time })
                });

                const result = await response.json();
                if (result.success) {
                    showModal('\u4E0A\u4F20\u6210\u529F', '\u{1F389}', '\u6210\u7EE9\u4E0A\u4F20\u6210\u529F\uFF01<br>\u60A8\u7684\u6210\u7EE9\u5DF2\u6DFB\u52A0\u5230\u6392\u884C\u699C\u4E2D\u3002');
                    if (currentLeaderboardDifficulty === difficulty) {
                        loadLeaderboard(difficulty);
                    }
                } else {
                    showModal('\u4E0A\u4F20\u5931\u8D25', '\u274C', '\u4E0A\u4F20\u5931\u8D25\uFF1A' + result.error);
                }
            } catch (error) {
                console.error('\u4E0A\u4F20\u6210\u7EE9\u5931\u8D25:', error);
                showModal('\u4E0A\u4F20\u5931\u8D25', '\u274C', '\u4E0A\u4F20\u5931\u8D25\uFF1A\u7F51\u7EDC\u8FDE\u63A5\u9519\u8BEF<br>\u8BF7\u68C0\u67E5\u7F51\u7EDC\u8FDE\u63A5\u540E\u91CD\u8BD5\u3002');
            }
        }

        // \u521D\u59CB\u5316
        window.addEventListener('DOMContentLoaded', () => {
            game = new MinesweeperGame();
            game.initGame(); // \u786E\u4FDD\u6E38\u620F\u677F\u88AB\u521B\u5EFA
            loadLeaderboard('beginner');

            // \u6DFB\u52A0\u7A97\u53E3\u5927\u5C0F\u53D8\u5316\u76D1\u542C\u5668
            window.addEventListener('resize', () => {
                if (game) {
                    game.calculateOptimalCellSize();
                }
            });
        });
    <\/script>
</body>
</html>`;
}
__name(getGameHTML, "getGameHTML");

// node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-JYa50Q/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = simple_default;

// node_modules/wrangler/templates/middleware/common.ts
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-JYa50Q/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof __Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
__name(__Facade_ScheduledController__, "__Facade_ScheduledController__");
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = (request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    };
    #dispatcher = (type, init) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    };
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
//# sourceMappingURL=simple.js.map
