{"name": "ufo", "version": "1.6.1", "description": "URL utils for humans", "repository": "unjs/ufo", "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.mjs"}, "./*": "./*"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "automd && unbuild", "automd": "automd", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint --fix . && prettier -w src test", "prepack": "pnpm build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && vitest run --typecheck"}, "devDependencies": {"@types/node": "^22.14.0", "@vitest/coverage-v8": "^3.1.1", "automd": "^0.4.0", "changelogen": "^0.6.1", "eslint": "^9.24.0", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.5.3", "typescript": "^5.8.3", "unbuild": "^3.5.0", "untyped": "^2.0.0", "vitest": "^3.1.1"}, "packageManager": "pnpm@10.7.1"}